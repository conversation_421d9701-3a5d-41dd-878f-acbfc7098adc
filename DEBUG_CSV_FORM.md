# Debug Instructions for CSV Form Issue

## Test URLs to Visit:

1. **Test Page** (simple debugging page):
   http://localhost:3000/dashboard/data_sources/test
   
2. **New Data Source Form** (actual form):
   http://localhost:3000/dashboard/data_sources/new

## What to Check in Browser Console:

1. Open browser developer tools (F12 or right-click -> Inspect)
2. Go to the Console tab
3. You should see these debug messages:
   - "DataSourceForm controller connected"
   - "Found radios: [number]"
   - When clicking CSV: "Radio changed via addEventListener: csv"
   - "updateConnectionFields called with: csv"
   - "loadConnectionFields called for: csv"

## Things to Try:

1. Click on the CSV radio button
2. Check the console for any JavaScript errors
3. Check the Network tab to see if a request is made to:
   `/dashboard/data_sources/connection_fields/csv`

## Expected Behavior:

When you click CSV, you should see:
- A loading spinner briefly
- Then a form with CSV-specific fields:
  - File upload input
  - CSV URL input (optional)
  - Delimiter selection
  - Header row checkbox
  - Encoding selection

## If Nothing Happens:

1. Check for JavaScript errors in console
2. Verify Stimulus is loaded by typing in console:
   ```javascript
   Stimulus.controllers
   ```
3. Check if the controller is registered:
   ```javascript
   document.querySelector('[data-controller="data-source-form"]')
   ```

## Server Logs to Watch:

Look for requests like:
```
GET /dashboard/data_sources/connection_fields/csv
```

The response should be 200 OK, not 404 or 500.