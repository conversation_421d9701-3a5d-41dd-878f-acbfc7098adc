// Data Reflow Platform - Complete SaaS Demo with Auth Flow & Dashboard (FIXED)

// Application data using provided JSON and additional platform data
const platformData = {
  // From provided JSON
  platform_stats: {
    customers: "2,500+",
    data_processed: "10TB+",
    uptime: "99.9%",
    connectors: "600+"
  },
  testimonials: [
    {
      name: "<PERSON>",
      role: "Data Director",
      company: "TechCorp",
      feedback: "Data Reflow transformed our analytics capabilities. We saw 40% improvement in decision-making speed.",
      rating: 5
    },
    {
      name: "<PERSON>", 
      role: "CTO",
      company: "GrowthCo",
      feedback: "The smart data mapping saved us hundreds of hours. ROI was evident within the first month.",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "Analytics Manager", 
      company: "RetailPlus",
      feedback: "Quality monitoring catches issues before they impact our business. Invaluable for our operations.",
      rating: 5
    }
  ],
  pricing_plans: [
    {
      name: "Starter",
      price: 29,
      period: "month",
      description: "Perfect for small teams getting started",
      features: ["Up to 3 data sources", "Basic ETL pipelines", "Standard support", "5GB storage"],
      popular: false
    },
    {
      name: "Professional", 
      price: 99,
      period: "month",
      description: "Most popular for growing businesses",
      features: ["Up to 25 data sources", "Advanced ETL/ELT", "Priority support", "100GB storage", "Smart data mapping", "Quality monitoring"],
      popular: true
    },
    {
      name: "Enterprise",
      price: 299,
      period: "month", 
      description: "For large organizations with complex needs",
      features: ["Unlimited data sources", "Custom connectors", "24/7 support", "1TB storage", "Advanced analytics", "White-label options"],
      popular: false
    }
  ],
  data_sources: [
    {name: "Salesforce CRM", type: "SaaS", status: "active", records: 15420, quality: 92, last_sync: "2m ago"},
    {name: "PostgreSQL DB", type: "Database", status: "error", records: 50210, quality: 85, last_sync: "1h ago"}, 
    {name: "Google Analytics", type: "Web Analytics", status: "active", records: 32345, quality: 97, last_sync: "now"},
    {name: "Shopify Store", type: "eCommerce", status: "active", records: 8780, quality: 89, last_sync: "15m ago"}
  ],
  pipelines: [
    {name: "Daily Sales ETL", type: "ETL", status: "success", last_run: "7:00 AM", success_rate: 99.2, duration: "3m 20s"},
    {name: "Web Analytics ELT", type: "ELT", status: "running", last_run: "10m ago", success_rate: 98.6, duration: "1m 55s"},
    {name: "CLV Prediction", type: "ML", status: "success", last_run: "2h ago", success_rate: 100, duration: "5m 42s"}
  ],
  analytics: {
    revenue: 245000,
    revenue_change: 14,
    conversion_rate: 4.5,
    conversion_change: 0.4,
    new_customers: 310,
    customers_change: 18,
    clv: 1084,
    clv_change: 7,
    retention_rate: 87,
    retention_change: -1,
    ad_roi: 3.6,
    roi_change: 0.2
  },

  // Additional platform features
  features: [
    {
      icon: "database",
      title: "Smart Data Mapping",
      description: "AI-powered field mapping with 95% accuracy. Automatically detects relationships and suggests optimal transformations.",
      benefits: ["Reduces setup time by 80%", "Eliminates mapping errors", "Self-learning algorithms"]
    },
    {
      icon: "check-double", 
      title: "Quality Scoring & Monitoring",
      description: "Real-time data quality assessment with automated anomaly detection and cleansing recommendations.",
      benefits: ["Improves accuracy by 40%", "Automated issue detection", "Compliance reporting"]
    },
    {
      icon: "cogs",
      title: "ETL/ELT Processing", 
      description: "Flexible data transformation supporting both ETL and ELT patterns for any scale of operation.",
      benefits: ["Handles billions of records", "Real-time & batch processing", "Custom transformations"]
    },
    {
      icon: "plug",
      title: "600+ Pre-built Connectors",
      description: "Extensive connector library for all major databases, APIs, and cloud services with no-code setup.",
      benefits: ["Instant connectivity", "No coding required", "Regular updates"]
    },
    {
      icon: "chart-line",
      title: "Advanced Analytics",
      description: "Built-in analytics dashboard with predictive insights and custom visualization capabilities.",
      benefits: ["Real-time insights", "Custom dashboards", "Predictive analytics"]
    },
    {
      icon: "shield-alt",
      title: "Enterprise Security",
      description: "Bank-grade security with end-to-end encryption, role-based access, and compliance certifications.",
      benefits: ["End-to-end encryption", "Role-based access", "SOC 2 compliant"]
    }
  ]
};

// Color schemes configuration (5 themes as requested)
const colorSchemes = {
  default: {
    name: "Default Teal",
    primary: "#21808D",
    secondary: "#5E5240",
    accent: "#A84B2F"
  },
  sunset: {
    name: "Sunset Energy",
    primary: "#6B46C1",
    secondary: "#FF6B35", 
    accent: "#FF8A80"
  },
  electric: {
    name: "Electric Modern",
    primary: "#0EA5E9",
    secondary: "#10B981",
    accent: "#FBBF24"
  },
  earthy: {
    name: "Earthy Professional", 
    primary: "#059669",
    secondary: "#DC6803",
    accent: "#D97706"
  },
  royal: {
    name: "Royal Elegance",
    primary: "#1E3A8A",
    secondary: "#EC4899",
    accent: "#6EE7B7"
  },
  vibrant: {
    name: "Vibrant Tech",
    primary: "#DB2777",
    secondary: "#06B6D4", 
    accent: "#84CC16"
  }
};

// Application state
let isDarkMode = false;
let currentColorScheme = 'default';
let currentPage = 'home';
let currentDashboardSection = 'overview';
let isMenuOpen = false;
let isColorSchemeDropdownOpen = false;
let currentUser = null; // In-memory session
let signupData = {}; // Multi-step signup data
let currentSignupStep = 0;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
  console.log('Initializing Data Reflow Platform...');
  
  initializeTheme();
  initializeColorScheme();
  initializeRouting();
  setupEventListeners();
  populateAllContent();
  setupScrollAnimations();
  showCurrentPage();
  
  // Show welcome message after a delay
  setTimeout(() => {
    const schemeName = colorSchemes[currentColorScheme].name;
    showNotification(`🎨 Welcome to Data Reflow! Using ${schemeName} theme`, 'info');
  }, 1000);
  
  console.log('Data Reflow Platform initialized successfully');
});

// Theme and Color Scheme Management
function initializeTheme() {
  const savedTheme = localStorage.getItem('dataReflow-theme');
  const systemDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
  
  isDarkMode = savedTheme ? savedTheme === 'dark' : systemDarkMode;
  updateTheme();
  setupNavbarScroll();
}

function initializeColorScheme() {
  const savedScheme = localStorage.getItem('dataReflow-color-scheme') || 'default';
  currentColorScheme = savedScheme;
  applyColorScheme(savedScheme, false);
  updateColorSchemeSelector();
}

function updateTheme() {
  const themeToggle = document.getElementById('themeToggle');
  if (!themeToggle) return;
  
  const icon = themeToggle.querySelector('i');
  const rootEl = document.documentElement;
  
  if (isDarkMode) {
    rootEl.setAttribute('data-theme', 'dark');
    if (icon) icon.className = 'fas fa-sun';
  } else {
    rootEl.setAttribute('data-theme', 'light');
    if (icon) icon.className = 'fas fa-moon';
  }
}

function applyColorScheme(scheme, animate = true) {
  const rootEl = document.documentElement;
  
  if (animate) {
    rootEl.classList.add('scheme-switching');
  }
  
  rootEl.setAttribute('data-color-scheme', scheme);
  currentColorScheme = scheme;
  
  if (animate) {
    setTimeout(() => {
      rootEl.classList.remove('scheme-switching');
    }, 500);
  }
  
  localStorage.setItem('dataReflow-color-scheme', scheme);
}

function updateColorSchemeSelector() {
  const options = document.querySelectorAll('.color-scheme-option');
  options.forEach(option => {
    const scheme = option.dataset.scheme;
    option.classList.toggle('active', scheme === currentColorScheme);
  });
}

function toggleTheme() {
  isDarkMode = !isDarkMode;
  updateTheme();
  localStorage.setItem('dataReflow-theme', isDarkMode ? 'dark' : 'light');
  showNotification(`Switched to ${isDarkMode ? 'dark' : 'light'} mode`, 'success');
}

// Routing System
function initializeRouting() {
  window.addEventListener('popstate', function(e) {
    const page = e.state ? e.state.page : 'home';
    navigateToPage(page, false);
  });
  
  history.replaceState({ page: currentPage }, '', '#' + currentPage);
}

function navigateToPage(pageName, addToHistory = true) {
  console.log('Navigating to page:', pageName);
  
  const validPages = ['home', 'pricing', 'solutions', 'enterprise', 'signup', 'dashboard'];
  if (!validPages.includes(pageName)) {
    console.error('Invalid page:', pageName);
    return;
  }
  
  // Hide current page
  const currentPageEl = document.querySelector('.page.active');
  if (currentPageEl) {
    currentPageEl.classList.remove('active');
  }
  
  // Show new page
  const newPageEl = document.getElementById(pageName + '-page');
  if (newPageEl) {
    newPageEl.classList.add('active');
    currentPage = pageName;
    
    // Update navigation
    updateNavigation();
    
    // Add to browser history
    if (addToHistory) {
      history.pushState({ page: pageName }, '', '#' + pageName);
    }
    
    // Scroll to top
    window.scrollTo(0, 0);
    
    // Populate page-specific content
    populatePageContent(pageName);
    
    // Close mobile menu if open
    if (isMenuOpen) {
      closeMobileMenu();
    }
    
    showNotification(`📄 Navigated to ${pageName} page`, 'info');
  } else {
    console.error('Page element not found:', pageName + '-page');
  }
}

function updateNavigation() {
  const navLinks = document.querySelectorAll('.nav-link[data-page]');
  navLinks.forEach(link => {
    const page = link.dataset.page;
    link.classList.toggle('active', page === currentPage);
  });
}

function showCurrentPage() {
  const hash = window.location.hash.replace('#', '');
  const validPages = ['home', 'pricing', 'solutions', 'enterprise', 'signup', 'dashboard'];
  
  if (hash && validPages.includes(hash)) {
    navigateToPage(hash, false);
  } else {
    navigateToPage('home', false);
  }
}

// Event Listeners Setup - FIXED
function setupEventListeners() {
  console.log('Setting up event listeners...');
  
  // Theme toggle
  const themeToggle = document.getElementById('themeToggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      toggleTheme();
    });
  }

  // Color scheme selector - FIXED
  const colorSchemeToggle = document.getElementById('colorSchemeToggle');
  if (colorSchemeToggle) {
    colorSchemeToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      toggleColorSchemeDropdown();
    });
  }
  
  // Color scheme options - FIXED
  const colorSchemeOptions = document.querySelectorAll('.color-scheme-option');
  colorSchemeOptions.forEach(option => {
    option.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      const scheme = this.dataset.scheme;
      applyColorScheme(scheme);
      updateColorSchemeSelector();
      closeColorSchemeDropdown();
      showNotification(`✨ Switched to ${colorSchemes[scheme].name} theme`, 'success');
    });
  });
  
  // Close dropdown when clicking outside
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.color-scheme-selector')) {
      closeColorSchemeDropdown();
    }
  });

  // Mobile menu toggle
  const menuToggle = document.getElementById('menuToggle');
  const navMenu = document.getElementById('navMenu');
  
  if (menuToggle && navMenu) {
    menuToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      isMenuOpen = !isMenuOpen;
      navMenu.classList.toggle('active', isMenuOpen);
      menuToggle.classList.toggle('active', isMenuOpen);
      document.body.style.overflow = isMenuOpen ? 'hidden' : '';
    });
  }

  // Navigation handling - FIXED
  document.addEventListener('click', function(e) {
    const target = e.target;
    
    // Handle navigation links
    if (target.matches('[data-page]') || target.closest('[data-page]')) {
      e.preventDefault();
      e.stopPropagation();
      const pageElement = target.closest('[data-page]') || target;
      const page = pageElement.getAttribute('data-page');
      if (page && ['home', 'pricing', 'solutions', 'enterprise'].includes(page)) {
        navigateToPage(page);
      }
      return;
    }
    
    // Handle CTA buttons
    handleCTAButtons(e);
  });

  // Modal functionality - FIXED
  setupModalHandlers();
  
  // Signup wizard - FIXED
  setupSignupWizard();
  
  // Dashboard
  setupDashboard();
  
  // Auth buttons - FIXED
  setupAuthButtons();
  
  console.log('Event listeners setup complete');
}

function setupAuthButtons() {
  // Login button
  const loginBtn = document.getElementById('loginBtn');
  if (loginBtn) {
    loginBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      showLoginModal();
    });
  }
  
  // Signup link
  const signupLink = document.getElementById('signupLink');
  if (signupLink) {
    signupLink.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      navigateToPage('signup');
    });
  }
  
  // Start trial buttons
  const startTrialBtn = document.getElementById('startTrialBtn');
  if (startTrialBtn) {
    startTrialBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      navigateToPage('signup');
    });
  }
  
  // Watch demo button
  const watchDemoBtn = document.getElementById('watchDemoBtn');
  if (watchDemoBtn) {
    watchDemoBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      showDemoModal();
    });
  }
  
  // Login form
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    loginForm.addEventListener('submit', function(e) {
      e.preventDefault();
      handleLogin();
    });
  }
  
  // Logout button
  const logoutBtn = document.getElementById('logoutBtn');
  if (logoutBtn) {
    logoutBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      handleLogout();
    });
  }
  
  // Signup from login
  const signupFromLogin = document.getElementById('signupFromLogin');
  if (signupFromLogin) {
    signupFromLogin.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      closeLoginModal();
      navigateToPage('signup');
    });
  }
}

// Authentication System (In-Memory) - FIXED
function handleLogin() {
  const email = document.getElementById('loginEmail')?.value;
  const password = document.getElementById('loginPassword')?.value;
  
  if (!email || !password) {
    showNotification('Please fill in all fields', 'error');
    return;
  }
  
  // Simulate login (no real backend)
  currentUser = {
    email: email,
    name: email.split('@')[0],
    plan: 'Professional',
    loginTime: new Date()
  };
  
  closeLoginModal();
  navigateToPage('dashboard');
  showNotification(`Welcome back, ${currentUser.name}!`, 'success');
}

function handleLogout() {
  currentUser = null;
  navigateToPage('home');
  showNotification('Logged out successfully', 'info');
}

function showLoginModal() {
  const modal = document.getElementById('loginModal');
  if (modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
  }
}

function closeLoginModal() {
  const modal = document.getElementById('loginModal');
  if (modal) {
    modal.classList.remove('active');
    document.body.style.overflow = '';
    
    // Clear form
    const form = document.getElementById('loginForm');
    if (form) {
      form.reset();
    }
  }
}

// Signup Wizard (Multi-Step) - FIXED
function setupSignupWizard() {
  const nextBtn = document.getElementById('nextStep');
  const prevBtn = document.getElementById('prevStep');
  
  if (nextBtn) {
    nextBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      nextSignupStep();
    });
  }
  
  if (prevBtn) {
    prevBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      prevSignupStep();
    });
  }
}

function resetSignupWizard() {
  currentSignupStep = 0;
  signupData = {};
  updateSignupStep();
  
  // Clear all form fields
  const form = document.getElementById('signupForm');
  if (form) {
    const inputs = form.querySelectorAll('input, select');
    inputs.forEach(input => {
      if (input.type === 'radio') {
        input.checked = false;
      } else {
        input.value = '';
      }
    });
    
    // Set default plan selection
    const professionalPlan = form.querySelector('input[name="plan"][value="Professional"]');
    if (professionalPlan) {
      professionalPlan.checked = true;
    }
  }
}

function nextSignupStep() {
  console.log('Next step clicked, current step:', currentSignupStep);
  
  if (!validateCurrentStep()) {
    return;
  }
  
  // Store current step data
  saveCurrentStepData();
  
  if (currentSignupStep < 3) {
    currentSignupStep++;
    updateSignupStep();
    showNotification(`Step ${currentSignupStep + 1} of 4`, 'info');
  } else {
    // Final step - create account
    completeSignup();
  }
}

function prevSignupStep() {
  if (currentSignupStep > 0) {
    currentSignupStep--;
    updateSignupStep();
    showNotification(`Back to Step ${currentSignupStep + 1}`, 'info');
  }
}

function updateSignupStep() {
  console.log('Updating signup step to:', currentSignupStep);
  
  // Hide all steps
  document.querySelectorAll('.wizard-step').forEach(step => {
    step.classList.remove('active');
  });
  
  // Show current step
  const currentStep = document.querySelector(`[data-step="${currentSignupStep}"]`);
  if (currentStep) {
    currentStep.classList.add('active');
  } else {
    console.error('Step not found:', currentSignupStep);
  }
  
  // Update progress bar
  const progress = document.getElementById('wizardProgress');
  if (progress) {
    const percentage = ((currentSignupStep + 1) / 4) * 100;
    progress.style.width = `${percentage}%`;
  }
  
  // Update buttons
  const nextBtn = document.getElementById('nextStep');
  const prevBtn = document.getElementById('prevStep');
  
  if (prevBtn) {
    prevBtn.style.display = currentSignupStep === 0 ? 'none' : 'inline-flex';
  }
  
  if (nextBtn) {
    nextBtn.textContent = currentSignupStep === 3 ? 'Create Account' : 'Next';
  }
  
  // Update verify summary for final step
  if (currentSignupStep === 3) {
    updateVerifySummary();
  }
}

function validateCurrentStep() {
  const currentStep = document.querySelector('.wizard-step.active');
  if (!currentStep) {
    console.error('No active step found');
    return false;
  }
  
  const requiredInputs = currentStep.querySelectorAll('[required]');
  console.log('Validating', requiredInputs.length, 'required inputs');
  
  for (let input of requiredInputs) {
    if (input.type === 'radio') {
      const radioGroup = currentStep.querySelectorAll(`input[name="${input.name}"]`);
      const isChecked = Array.from(radioGroup).some(radio => radio.checked);
      if (!isChecked) {
        showNotification('Please select an option', 'error');
        return false;
      }
    } else if (input.type === 'checkbox') {
      if (!input.checked) {
        showNotification('Please accept the terms and conditions', 'error');
        return false;
      }
    } else if (!input.value.trim()) {
      input.focus();
      showNotification('Please fill in all required fields', 'error');
      return false;
    }
    
    if (input.type === 'email' && !isValidEmail(input.value)) {
      input.focus();
      showNotification('Please enter a valid email address', 'error');
      return false;
    }
    
    if (input.type === 'password' && input.value.length < 8) {
      input.focus();
      showNotification('Password must be at least 8 characters long', 'error');
      return false;
    }
  }
  
  console.log('Validation passed');
  return true;
}

function saveCurrentStepData() {
  console.log('Saving step data for step:', currentSignupStep);
  
  switch (currentSignupStep) {
    case 0:
      signupData.orgName = document.getElementById('orgName')?.value;
      signupData.orgSize = document.getElementById('orgSize')?.value;
      break;
    case 1:
      signupData.firstName = document.getElementById('firstName')?.value;
      signupData.lastName = document.getElementById('lastName')?.value;
      signupData.email = document.getElementById('email')?.value;
      signupData.password = document.getElementById('password')?.value;
      break;
    case 2:
      const selectedPlan = document.querySelector('input[name="plan"]:checked');
      signupData.plan = selectedPlan ? selectedPlan.value : 'Professional';
      break;
  }
  
  console.log('Saved signup data:', signupData);
}

function updateVerifySummary() {
  const summary = document.getElementById('verifySummary');
  if (summary) {
    summary.innerHTML = `
      <strong>Organization:</strong> ${signupData.orgName} (${signupData.orgSize})<br>
      <strong>Name:</strong> ${signupData.firstName} ${signupData.lastName}<br>
      <strong>Email:</strong> ${signupData.email}<br>
      <strong>Plan:</strong> ${signupData.plan}
    `;
  }
}

function completeSignup() {
  // Check terms acceptance
  const termsAccepted = document.getElementById('termsAccepted')?.checked;
  if (!termsAccepted) {
    showNotification('Please accept the Terms of Service and Privacy Policy', 'error');
    return;
  }
  
  // Simulate account creation
  currentUser = {
    ...signupData,
    name: `${signupData.firstName} ${signupData.lastName}`,
    signupTime: new Date()
  };
  
  // Reset signup data
  signupData = {};
  currentSignupStep = 0;
  
  navigateToPage('dashboard');
  showNotification('🎉 Account created successfully! Welcome to Data Reflow!', 'success');
}

function isValidEmail(email) {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

// Dashboard System
function setupDashboard() {
  // Sidebar navigation
  const sidebarLinks = document.querySelectorAll('.sidebar-link');
  sidebarLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      const section = this.dataset.section;
      if (section) {
        switchDashboardSection(section);
      }
    });
  });
  
  // Mobile sidebar toggle
  const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
  if (mobileSidebarToggle) {
    mobileSidebarToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      const sidebar = document.getElementById('dashboardSidebar');
      if (sidebar) {
        sidebar.classList.toggle('active');
      }
    });
  }
}

function switchDashboardSection(section) {
  // Update sidebar
  document.querySelectorAll('.sidebar-link').forEach(link => {
    link.classList.toggle('active', link.dataset.section === section);
  });
  
  // Update content
  document.querySelectorAll('.dashboard-section').forEach(sec => {
    sec.classList.toggle('active', sec.id === `section-${section}`);
  });
  
  currentDashboardSection = section;
  
  // Load section-specific data
  loadDashboardData(section);
  
  showNotification(`📊 Switched to ${section} section`, 'info');
}

function loadDashboardData(section) {
  switch (section) {
    case 'overview':
      loadKPIData();
      loadCharts();
      break;
    case 'sources':
      loadDataSources();
      break;
    case 'pipelines':
      loadPipelines();
      break;
    case 'quality':
      loadQualityMetrics();
      break;
    case 'analytics':
      loadAnalytics();
      break;
    case 'settings':
      loadUserSettings();
      break;
  }
}

function loadKPIData() {
  const data = platformData.analytics;
  
  const revenueEl = document.getElementById('kpiRevenue');
  const conversionEl = document.getElementById('kpiConversion');
  const customersEl = document.getElementById('kpiCustomers');
  const ltvEl = document.getElementById('kpiLTV');
  
  if (revenueEl) {
    animateValue(revenueEl, 0, data.revenue, 1000, (val) => `$${Math.round(val).toLocaleString()}`);
  }
  
  if (conversionEl) {
    animateValue(conversionEl, 0, data.conversion_rate, 1000, (val) => `${val.toFixed(1)}%`);
  }
  
  if (customersEl) {
    animateValue(customersEl, 0, data.new_customers, 1000, (val) => Math.round(val).toString());
  }
  
  if (ltvEl) {
    animateValue(ltvEl, 0, data.clv, 1000, (val) => `$${Math.round(val).toLocaleString()}`);
  }
}

function loadCharts() {
  // Simple chart simulation using divs (placeholder for real charting library)
  createSimpleBarChart('revenueChart', [65, 80, 45, 90, 70, 85, 95]);
  createSimpleBarChart('volumeChart', [40, 60, 35, 75, 55, 70, 80]);
}

function createSimpleBarChart(canvasId, data) {
  const canvas = document.getElementById(canvasId);
  if (!canvas) return;
  
  // Replace canvas with a simple div-based chart
  const chartContainer = canvas.parentNode;
  chartContainer.innerHTML = '';
  
  const chart = document.createElement('div');
  chart.style.display = 'flex';
  chart.style.alignItems = 'end';
  chart.style.height = '100%';
  chart.style.gap = '4px';
  chart.style.padding = '16px';
  
  data.forEach(value => {
    const bar = document.createElement('div');
    bar.style.flex = '1';
    bar.style.backgroundColor = 'var(--color-primary)';
    bar.style.height = `${value}%`;
    bar.style.borderRadius = '2px 2px 0 0';
    bar.style.minHeight = '10px';
    bar.style.transition = 'height 0.6s ease-out';
    chart.appendChild(bar);
  });
  
  chartContainer.appendChild(chart);
}

function loadDataSources() {
  const sourcesGrid = document.getElementById('sourcesGrid');
  if (!sourcesGrid) return;
  
  sourcesGrid.innerHTML = '';
  
  platformData.data_sources.forEach(source => {
    const card = document.createElement('div');
    card.className = 'source-card';
    
    const statusClass = source.status === 'active' ? 'status-active' : 'status-error';
    
    card.innerHTML = `
      <div class="source-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h4 style="margin: 0; color: var(--color-text);">${source.name}</h4>
        <span class="${statusClass}" style="text-transform: capitalize;">${source.status}</span>
      </div>
      <div class="source-details" style="color: var(--color-text-secondary); font-size: 14px;">
        <p style="margin: 8px 0;"><strong>Type:</strong> ${source.type}</p>
        <p style="margin: 8px 0;"><strong>Records:</strong> ${source.records.toLocaleString()}</p>
        <p style="margin: 8px 0;"><strong>Quality:</strong> ${source.quality}%</p>
        <p style="margin: 8px 0;"><strong>Last Sync:</strong> ${source.last_sync}</p>
      </div>
    `;
    
    sourcesGrid.appendChild(card);
  });
}

function loadPipelines() {
  const pipelinesList = document.getElementById('pipelinesList');
  if (!pipelinesList) return;
  
  pipelinesList.innerHTML = '';
  
  platformData.pipelines.forEach(pipeline => {
    const item = document.createElement('div');
    item.className = 'pipeline-item';
    
    const statusClass = pipeline.status === 'success' ? 'status-success' : 
                       pipeline.status === 'running' ? 'status-running' : 'status-error';
    
    item.innerHTML = `
      <div class="pipeline-info">
        <h4 style="margin: 0 0 8px 0; color: var(--color-text);">${pipeline.name}</h4>
        <p style="margin: 0; color: var(--color-text-secondary); font-size: 14px;">Type: ${pipeline.type} | Success Rate: ${pipeline.success_rate}% | Duration: ${pipeline.duration}</p>
      </div>
      <div class="pipeline-status" style="text-align: right;">
        <span class="${statusClass}" style="text-transform: capitalize; font-weight: 500;">${pipeline.status}</span>
        <br>
        <small style="color: var(--color-text-secondary);">Last run: ${pipeline.last_run}</small>
      </div>
    `;
    
    pipelinesList.appendChild(item);
  });
}

function loadQualityMetrics() {
  const qualityMetrics = document.getElementById('qualityMetrics');
  if (!qualityMetrics) return;
  
  qualityMetrics.innerHTML = '';
  
  const metrics = [
    { label: 'Data Completeness', value: 92 },
    { label: 'Data Accuracy', value: 89 },
    { label: 'Data Consistency', value: 85 },
    { label: 'Data Timeliness', value: 96 }
  ];
  
  metrics.forEach(metric => {
    const card = document.createElement('div');
    card.className = 'quality-metric';
    
    card.innerHTML = `
      <h4 style="margin: 0 0 16px 0; color: var(--color-text);">${metric.label}</h4>
      <div class="metric-value" style="font-size: 2rem; font-weight: bold; color: var(--color-primary); margin-bottom: 12px;">${metric.value}%</div>
      <div class="metric-bar" style="width: 100%; height: 8px; background: var(--color-border); border-radius: 4px; overflow: hidden;">
        <div class="metric-fill" style="height: 100%; width: ${metric.value}%; background: var(--color-primary); border-radius: 4px; transition: width 0.6s ease;"></div>
      </div>
    `;
    
    qualityMetrics.appendChild(card);
  });
}

function loadAnalytics() {
  // Create a simple pie chart simulation
  const segmentChart = document.getElementById('segmentChart');
  if (segmentChart) {
    const chartContainer = segmentChart.parentNode;
    chartContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 200px;">
        <div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(
          var(--color-primary) 0deg 120deg,
          var(--scheme-secondary) 120deg 240deg,
          var(--scheme-accent) 240deg 360deg
        ); display: flex; align-items: center; justify-content: center;">
          <div style="width: 80px; height: 80px; background: var(--color-surface); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; color: var(--color-text);">
            3 Segments
          </div>
        </div>
      </div>
    `;
  }
}

function loadUserSettings() {
  if (!currentUser) return;
  
  const profileName = document.getElementById('profileName');
  const profileEmail = document.getElementById('profileEmail');
  
  if (profileName) {
    profileName.value = currentUser.name || '';
  }
  
  if (profileEmail) {
    profileEmail.value = currentUser.email || '';
  }
  
  // Update user info in sidebar
  const userInfo = document.getElementById('userInfo');
  if (userInfo && currentUser) {
    const userName = userInfo.querySelector('.user-name');
    const userPlan = userInfo.querySelector('.user-plan');
    
    if (userName) userName.textContent = currentUser.name;
    if (userPlan) userPlan.textContent = `${currentUser.plan} Plan`;
  }
}

function animateValue(element, start, end, duration, formatter = (val) => val) {
  const startTime = performance.now();
  const range = end - start;
  
  function updateValue(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const current = start + (range * progress);
    
    element.textContent = formatter(current);
    
    if (progress < 1) {
      requestAnimationFrame(updateValue);
    }
  }
  
  requestAnimationFrame(updateValue);
}

// Content Population
function populateAllContent() {
  populateFeatures();
  populateTestimonials();
}

function populatePageContent(pageName) {
  switch(pageName) {
    case 'signup':
      resetSignupWizard();
      break;
    case 'dashboard':
      if (currentUser) {
        loadDashboardData(currentDashboardSection);
        loadUserSettings();
      } else {
        showLoginModal();
        navigateToPage('home');
      }
      break;
    case 'pricing':
      populatePricingPage();
      break;
  }
}

function populatePricingPage() {
  const pricingContent = document.getElementById('pricingContent');
  if (pricingContent) {
    pricingContent.innerHTML = `
      <div class="pricing-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 32px; margin-bottom: 48px;">
        ${platformData.pricing_plans.map((tier, index) => `
          <div class="pricing-card ${tier.popular ? 'popular' : ''}" style="background: rgba(var(--color-surface), 0.9); backdrop-filter: blur(20px); border: 1px solid var(--color-border); border-radius: 12px; padding: 32px; text-align: center; position: relative; transition: all 0.3s ease;">
            ${tier.popular ? '<div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: var(--color-primary); color: white; padding: 4px 16px; border-radius: 20px; font-size: 12px; font-weight: bold;">Most Popular</div>' : ''}
            <h3 style="margin-bottom: 16px; color: var(--color-text);">${tier.name}</h3>
            <div style="font-size: 3rem; font-weight: bold; color: var(--color-primary); margin: 16px 0;">$${tier.price}</div>
            <div style="color: var(--color-text-secondary); margin-bottom: 16px;">per ${tier.period}</div>
            <p style="color: var(--color-text-secondary); margin-bottom: 24px; min-height: 48px;">${tier.description}</p>
            <ul style="list-style: none; padding: 0; margin: 0 0 32px 0; text-align: left;">
              ${tier.features.map(feature => `
                <li style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; color: var(--color-text); font-size: 14px;">
                  <span style="color: var(--color-success); font-weight: bold; font-size: 16px;">✓</span>
                  ${feature}
                </li>
              `).join('')}
            </ul>
            <button class="btn ${tier.popular ? 'btn--primary' : 'btn--outline'} btn--full-width" onclick="handleTrialClick()">
              Start Free Trial
            </button>
          </div>
        `).join('')}
      </div>
      <div style="text-align: center; padding: 32px 0;">
        <h3 style="margin-bottom: 16px; color: var(--color-text);">Need a custom solution?</h3>
        <p style="color: var(--color-text-secondary); margin-bottom: 24px;">Contact our sales team for enterprise pricing and custom features.</p>
        <button class="btn btn--outline" onclick="showNotification('📞 Opening contact form...', 'info')">Contact Sales</button>
      </div>
    `;
  }
}

function handleTrialClick() {
  navigateToPage('signup');
}

// Make handleTrialClick global
window.handleTrialClick = handleTrialClick;

function populateFeatures() {
  const featuresGrid = document.getElementById('featuresGrid');
  if (!featuresGrid) return;

  featuresGrid.innerHTML = '';
  
  platformData.features.forEach((feature, index) => {
    const featureCard = createFeatureCard(feature, index);
    featuresGrid.appendChild(featureCard);
  });
}

function createFeatureCard(feature, index) {
  const card = document.createElement('div');
  card.className = 'feature-card fade-in';
  card.style.animationDelay = `${index * 0.1}s`;
  
  card.innerHTML = `
    <div class="feature-icon">
      <i class="fas fa-${feature.icon}"></i>
    </div>
    <h3 class="feature-title">${feature.title}</h3>
    <p class="feature-description">${feature.description}</p>
    <ul class="feature-benefits">
      ${feature.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
    </ul>
  `;
  
  return card;
}

function populateTestimonials() {
  const testimonialsGrid = document.getElementById('testimonialsGrid');
  if (!testimonialsGrid) return;

  testimonialsGrid.innerHTML = '';
  
  platformData.testimonials.forEach((testimonial, index) => {
    const testimonialCard = createTestimonialCard(testimonial, index);
    testimonialsGrid.appendChild(testimonialCard);
  });
}

function createTestimonialCard(testimonial, index) {
  const card = document.createElement('div');
  card.className = 'testimonial-card fade-in';
  card.style.animationDelay = `${index * 0.1}s`;
  
  const initials = testimonial.name.split(' ').map(n => n[0]).join('');
  
  card.innerHTML = `
    <div class="testimonial-quote">"${testimonial.feedback}"</div>
    <div class="testimonial-author">
      <div class="author-avatar">
        ${initials}
      </div>
      <div class="author-info">
        <div class="author-name">${testimonial.name}</div>
        <div class="author-title">${testimonial.role}, ${testimonial.company}</div>
        <div class="testimonial-rating">
          ${Array(testimonial.rating).fill().map(() => '<i class="fas fa-star star"></i>').join('')}
        </div>
      </div>
    </div>
  `;
  
  return card;
}

// Utility Functions - FIXED
function toggleColorSchemeDropdown() {
  const dropdown = document.getElementById('colorSchemeDropdown');
  if (dropdown) {
    isColorSchemeDropdownOpen = !isColorSchemeDropdownOpen;
    dropdown.classList.toggle('active', isColorSchemeDropdownOpen);
  }
}

function closeColorSchemeDropdown() {
  const dropdown = document.getElementById('colorSchemeDropdown');
  if (dropdown) {
    isColorSchemeDropdownOpen = false;
    dropdown.classList.remove('active');
  }
}

function closeMobileMenu() {
  const navMenu = document.getElementById('navMenu');
  const menuToggle = document.getElementById('menuToggle');
  
  if (navMenu) navMenu.classList.remove('active');
  if (menuToggle) menuToggle.classList.remove('active');
  document.body.style.overflow = '';
  isMenuOpen = false;
}

function handleCTAButtons(e) {
  const target = e.target.closest('button') || e.target;
  const text = target.textContent || '';
  
  if (text.includes('Start Free Trial') && target.id !== 'signupLink') {
    e.preventDefault();
    e.stopPropagation();
    navigateToPage('signup');
  }
  
  if (text.includes('Watch Demo')) {
    e.preventDefault();
    e.stopPropagation();
    showDemoModal();
  }
  
  if (text.includes('Contact Sales') || text.includes('Schedule Consultation')) {
    e.preventDefault();
    e.stopPropagation();
    showNotification('📅 Opening calendar booking...', 'info');
  }
  
  if (text.includes('Request Beta Access')) {
    e.preventDefault();
    e.stopPropagation();
    showNotification('📧 Beta access request submitted!', 'success');
  }
  
  if (text.includes('Start Interactive Demo')) {
    e.preventDefault();
    e.stopPropagation();
    showNotification('🚀 Loading interactive demo...', 'info');
    setTimeout(() => {
      showNotification('Demo completed! Sign up to access full platform.', 'success');
    }, 3000);
  }
}

function setupModalHandlers() {
  const demoModal = document.getElementById('demoModal');
  const demoModalClose = document.getElementById('modalClose');
  const demoModalOverlay = document.getElementById('modalOverlay');
  
  const loginModal = document.getElementById('loginModal');
  const loginModalClose = document.getElementById('loginClose');
  const loginModalOverlay = document.getElementById('loginOverlay');
  
  if (demoModalClose) {
    demoModalClose.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      closeDemoModal();
    });
  }
  
  if (demoModalOverlay) {
    demoModalOverlay.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      closeDemoModal();
    });
  }
  
  if (loginModalClose) {
    loginModalClose.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      closeLoginModal();
    });
  }
  
  if (loginModalOverlay) {
    loginModalOverlay.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      closeLoginModal();
    });
  }
  
  // Escape key handling - FIXED
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      if (demoModal && demoModal.classList.contains('active')) {
        closeDemoModal();
      }
      if (loginModal && loginModal.classList.contains('active')) {
        closeLoginModal();
      }
      if (isColorSchemeDropdownOpen) {
        closeColorSchemeDropdown();
      }
    }
  });
}

function showDemoModal() {
  const modal = document.getElementById('demoModal');
  if (modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
    showNotification('🎬 Demo modal opened!', 'success');
  }
}

function closeDemoModal() {
  const modal = document.getElementById('demoModal');
  if (modal) {
    modal.classList.remove('active');
    document.body.style.overflow = '';
  }
}

function setupNavbarScroll() {
  const navbar = document.getElementById('navbar');
  if (!navbar) return;
  
  let lastScrollY = window.scrollY;
  
  window.addEventListener('scroll', function() {
    const currentScrollY = window.scrollY;
    
    navbar.classList.toggle('scrolled', currentScrollY > 50);
    
    if (currentScrollY > lastScrollY && currentScrollY > 100) {
      navbar.style.transform = 'translateY(-100%)';
    } else {
      navbar.style.transform = 'translateY(0)';
    }
    
    lastScrollY = currentScrollY;
  });
}

function setupScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  const fadeElements = document.querySelectorAll('.fade-in');
  fadeElements.forEach(element => {
    observer.observe(element);
  });
}

function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  
  const colors = {
    success: 'var(--color-success)',
    error: 'var(--color-error)', 
    warning: 'var(--color-warning)',
    info: 'var(--color-primary)'
  };
  
  notification.style.cssText = `
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 12px 20px;
    background: ${colors[type]};
    color: var(--color-white);
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 2001;
    font-weight: 500;
    max-width: 350px;
    font-size: 14px;
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
  `;
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.style.animation = 'slideOutRight 0.3s ease-in';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 300);
  }, 4000);
}

// Add notification animations CSS
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
  @keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(notificationStyles);

// Handle window resize
let resizeTimeout;
window.addEventListener('resize', function() {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(function() {
    if (window.innerWidth > 768 && isMenuOpen) {
      closeMobileMenu();
    }
  }, 250);
});

// Enhanced interactivity tracking
document.addEventListener('DOMContentLoaded', function() {
  document.addEventListener('click', function(e) {
    const target = e.target.closest('button, .card, .sidebar-link, .color-scheme-option, [data-page]');
    if (target) {
      console.log('User interaction:', {
        element: target.className,
        page: currentPage,
        text: target.textContent?.slice(0, 50),
        colorScheme: currentColorScheme,
        darkMode: isDarkMode,
        user: currentUser?.email || 'anonymous',
        timestamp: new Date().toISOString()
      });
    }
  });
});