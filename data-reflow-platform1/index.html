<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Reflow Platform - Transform Your Data Into Competitive Advantage</title>
    <meta name="description" content="Enterprise-grade data processing platform with real-time analytics, automated ETL/ELT pipelines, and smart data mapping. Trusted by 2,500+ businesses worldwide.">
    <meta name="keywords" content="data analytics, business intelligence, ETL, ELT, data processing, real-time analytics, data pipeline, data integration">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://datareflow.com">
    <meta property="og:title" content="Data Reflow Platform - Transform Your Data Into Competitive Advantage">
    <meta property="og:description" content="Enterprise-grade data processing platform with real-time analytics, automated ETL/ELT pipelines, and smart data mapping.">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://datareflow.com">
    <meta property="twitter:title" content="Data Reflow Platform - Transform Your Data Into Competitive Advantage">
    <meta property="twitter:description" content="Enterprise-grade data processing platform with real-time analytics, automated ETL/ELT pipelines, and smart data mapping.">
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-content">
                <div class="nav-brand">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-stream"></i>
                        </div>
                        <span class="logo-text">Data Reflow</span>
                    </div>
                </div>
                <div class="nav-menu" id="navMenu">
                    <a href="#" class="nav-link" data-page="home">Platform</a>
                    <a href="#" class="nav-link" data-page="pricing">Pricing</a>
                    <a href="#" class="nav-link" data-page="solutions">Solutions</a>
                    <a href="#" class="nav-link" data-page="enterprise">Enterprise</a>
                    <button class="btn btn--outline btn--sm nav-cta" id="loginBtn">Sign In</button>
                    <button class="btn btn--primary btn--sm nav-cta" id="signupLink">Start Free Trial</button>
                </div>
                <div class="nav-controls">
                    <div class="theme-controls">
                        <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark/light mode" title="Toggle dark/light mode">
                            <i class="fas fa-moon"></i>
                        </button>
                        <div class="color-scheme-selector">
                            <button class="color-scheme-toggle" id="colorSchemeToggle" aria-label="Change color scheme" title="Choose color scheme">
                                <i class="fas fa-palette"></i>
                            </button>
                            <div class="color-scheme-dropdown" id="colorSchemeDropdown">
                                <div class="color-scheme-option active" data-scheme="default">
                                    <div class="scheme-preview">
                                        <span class="color-dot" style="background: #21808D"></span>
                                        <span class="color-dot" style="background: #5E5240"></span>
                                        <span class="color-dot" style="background: #A84B2F"></span>
                                    </div>
                                    <span class="scheme-name">Default Teal</span>
                                </div>
                                <div class="color-scheme-option" data-scheme="sunset">
                                    <div class="scheme-preview">
                                        <span class="color-dot" style="background: #6B46C1"></span>
                                        <span class="color-dot" style="background: #FF6B35"></span>
                                        <span class="color-dot" style="background: #FF8A80"></span>
                                    </div>
                                    <span class="scheme-name">Sunset Energy</span>
                                </div>
                                <div class="color-scheme-option" data-scheme="electric">
                                    <div class="scheme-preview">
                                        <span class="color-dot" style="background: #0EA5E9"></span>
                                        <span class="color-dot" style="background: #10B981"></span>
                                        <span class="color-dot" style="background: #FBBF24"></span>
                                    </div>
                                    <span class="scheme-name">Electric Modern</span>
                                </div>
                                <div class="color-scheme-option" data-scheme="earthy">
                                    <div class="scheme-preview">
                                        <span class="color-dot" style="background: #059669"></span>
                                        <span class="color-dot" style="background: #DC6803"></span>
                                        <span class="color-dot" style="background: #D97706"></span>
                                    </div>
                                    <span class="scheme-name">Earthy Professional</span>
                                </div>
                                <div class="color-scheme-option" data-scheme="royal">
                                    <div class="scheme-preview">
                                        <span class="color-dot" style="background: #1E3A8A"></span>
                                        <span class="color-dot" style="background: #EC4899"></span>
                                        <span class="color-dot" style="background: #6EE7B7"></span>
                                    </div>
                                    <span class="scheme-name">Royal Elegance</span>
                                </div>
                                <div class="color-scheme-option" data-scheme="vibrant">
                                    <div class="scheme-preview">
                                        <span class="color-dot" style="background: #DB2777"></span>
                                        <span class="color-dot" style="background: #06B6D4"></span>
                                        <span class="color-dot" style="background: #84CC16"></span>
                                    </div>
                                    <span class="scheme-name">Vibrant Tech</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="menu-toggle" id="menuToggle" aria-label="Toggle menu">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Router Container -->
    <div id="pageContainer" class="page-container">
        
        <!-- HOME PAGE -->
        <div id="home-page" class="page active">
            <!-- Hero Section -->
            <section class="hero">
                <div class="container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1 class="hero-title">Transform Your Data Into Competitive Advantage</h1>
                            <p class="hero-description">
                                Enterprise-grade data processing platform with real-time analytics, automated ETL/ELT pipelines, 
                                and smart data mapping. Trusted by 2,500+ businesses worldwide.
                            </p>
                            <div class="hero-stats">
                                <div class="stat">
                                    <div class="stat-number">2,500+</div>
                                    <div class="stat-label">Active Customers</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">10TB+</div>
                                    <div class="stat-label">Data Processed Daily</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">99.9%</div>
                                    <div class="stat-label">Uptime SLA</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">600+</div>
                                    <div class="stat-label">Pre-built Connectors</div>
                                </div>
                            </div>
                            <div class="hero-cta">
                                <button class="btn btn--primary btn--lg" id="startTrialBtn">
                                    <i class="fas fa-rocket"></i>
                                    Start Free Trial
                                </button>
                                <button class="btn btn--outline btn--lg" id="watchDemoBtn">
                                    <i class="fas fa-play"></i>
                                    Watch Demo
                                </button>
                            </div>
                        </div>
                        <div class="hero-visual">
                            <div class="dashboard-preview">
                                <div class="preview-header">
                                    <div class="preview-controls">
                                        <span class="control-dot red"></span>
                                        <span class="control-dot yellow"></span>
                                        <span class="control-dot green"></span>
                                    </div>
                                    <div class="preview-title">Data Reflow Dashboard</div>
                                </div>
                                <div class="preview-content">
                                    <div class="preview-sidebar">
                                        <div class="sidebar-item active">
                                            <i class="fas fa-chart-line"></i>
                                            <span>Overview</span>
                                        </div>
                                        <div class="sidebar-item">
                                            <i class="fas fa-database"></i>
                                            <span>Sources</span>
                                        </div>
                                        <div class="sidebar-item">
                                            <i class="fas fa-project-diagram"></i>
                                            <span>Pipelines</span>
                                        </div>
                                    </div>
                                    <div class="preview-main">
                                        <div class="preview-cards">
                                            <div class="preview-card">
                                                <div class="card-metric">$245k</div>
                                                <div class="card-label">Revenue</div>
                                            </div>
                                            <div class="preview-card">
                                                <div class="card-metric">4.5%</div>
                                                <div class="card-label">Conversion</div>
                                            </div>
                                        </div>
                                        <div class="preview-chart">
                                            <div class="chart-bars">
                                                <div class="chart-bar" style="height: 60%"></div>
                                                <div class="chart-bar" style="height: 80%"></div>
                                                <div class="chart-bar" style="height: 45%"></div>
                                                <div class="chart-bar" style="height: 90%"></div>
                                                <div class="chart-bar" style="height: 70%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="features">
                <div class="container">
                    <div class="section-header">
                        <h2>Everything You Need for Data Excellence</h2>
                        <p>Comprehensive data processing capabilities with enterprise-grade security and scalability.</p>
                    </div>
                    <div class="features-grid" id="featuresGrid">
                        <!-- Features will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Testimonials Section -->
            <section class="testimonials">
                <div class="container">
                    <div class="section-header">
                        <h2>Trusted by Industry Leaders</h2>
                        <p>See how companies like yours are transforming their data operations with Data Reflow.</p>
                    </div>
                    <div class="testimonials-grid" id="testimonialsGrid">
                        <!-- Testimonials will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Pricing Preview -->
            <section class="pricing-preview">
                <div class="container">
                    <div class="section-header">
                        <h2>Simple, Transparent Pricing</h2>
                        <p>Choose the plan that scales with your business. All plans include our core features.</p>
                    </div>
                    <div class="pricing-cta">
                        <button class="btn btn--primary btn--lg" data-page="pricing">View All Plans</button>
                    </div>
                </div>
            </section>
        </div>

        <!-- PRICING PAGE -->
        <div id="pricing-page" class="page">
            <section class="page-hero">
                <div class="container">
                    <div class="page-hero-content">
                        <h1>Pricing Plans</h1>
                        <p>Choose the perfect plan for your data processing needs. Start free, scale as you grow.</p>
                    </div>
                </div>
            </section>
            <section class="page-content">
                <div class="container">
                    <div id="pricingContent"></div>
                </div>
            </section>
        </div>

        <!-- SOLUTIONS PAGE -->
        <div id="solutions-page" class="page">
            <section class="page-hero">
                <div class="container">
                    <div class="page-hero-content">
                        <h1>Solutions</h1>
                        <p>Discover how Data Reflow solves complex data challenges across industries.</p>
                    </div>
                </div>
            </section>
            <section class="page-content">
                <div class="container">
                    <div class="solutions-grid">
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h3>E-commerce Analytics</h3>
                            <p>Unify customer data across all touchpoints for comprehensive behavioral insights and personalized experiences.</p>
                        </div>
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <h3>Healthcare Data</h3>
                            <p>HIPAA-compliant data processing for patient records, clinical trials, and healthcare analytics.</p>
                        </div>
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-university"></i>
                            </div>
                            <h3>Financial Services</h3>
                            <p>Risk assessment, fraud detection, and regulatory compliance with real-time financial data processing.</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- ENTERPRISE PAGE -->
        <div id="enterprise-page" class="page">
            <section class="page-hero">
                <div class="container">
                    <div class="page-hero-content">
                        <h1>Enterprise Solutions</h1>
                        <p>Scale to billions of records with enterprise-grade security, compliance, and dedicated support.</p>
                    </div>
                </div>
            </section>
            <section class="page-content">
                <div class="container">
                    <div class="enterprise-features">
                        <div class="enterprise-feature">
                            <i class="fas fa-shield-alt"></i>
                            <h3>Advanced Security</h3>
                            <p>End-to-end encryption, SSO integration, and role-based access controls.</p>
                        </div>
                        <div class="enterprise-feature">
                            <i class="fas fa-headset"></i>
                            <h3>Dedicated Support</h3>
                            <p>24/7 priority support with dedicated customer success managers.</p>
                        </div>
                        <div class="enterprise-feature">
                            <i class="fas fa-cogs"></i>
                            <h3>Custom Integrations</h3>
                            <p>Build custom connectors and transformations tailored to your systems.</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- SIGN-UP WIZARD PAGE -->
        <div id="signup-page" class="page">
            <section class="signup-wizard container">
                <div class="wizard-header">
                    <button class="wizard-close" id="wizardClose" aria-label="Close signup">
                        <i class="fas fa-times"></i>
                    </button>
                    <h1 class="wizard-title">Create Your Free Account</h1>
                    <p class="wizard-sub">Complete the steps below to get started with Data Reflow.</p>
                </div>

                <div class="wizard-progress">
                    <div class="progress-bar" id="wizardProgress"></div>
                </div>

                <form id="signupForm">
                    <!-- STEP 1: ORGANIZATION  -->
                    <div class="wizard-step active" data-step="0">
                        <h3>Organization Details</h3>
                        <div class="form-group">
                            <label class="form-label" for="orgName">Organization name *</label>
                            <input type="text" id="orgName" class="form-control" placeholder="Acme Corp" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="orgSize">Company size *</label>
                            <select id="orgSize" class="form-control" required>
                                <option value="">Select company size</option>
                                <option value="1-10">1-10 employees</option>
                                <option value="11-50">11-50 employees</option>
                                <option value="51-200">51-200 employees</option>
                                <option value="201-500">201-500 employees</option>
                                <option value="500+">500+ employees</option>
                            </select>
                        </div>
                    </div>

                    <!-- STEP 2: USER INFO -->
                    <div class="wizard-step" data-step="1">
                        <h3>Your Details</h3>
                        <div class="form-group">
                            <label class="form-label" for="firstName">First name *</label>
                            <input type="text" id="firstName" class="form-control" placeholder="Jane" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="lastName">Last name *</label>
                            <input type="text" id="lastName" class="form-control" placeholder="Doe" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="email">Business email *</label>
                            <input type="email" id="email" class="form-control" placeholder="<EMAIL>" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="password">Password *</label>
                            <input type="password" id="password" class="form-control" placeholder="Min 8 characters" required minlength="8">
                        </div>
                    </div>

                    <!-- STEP 3: PLAN  -->
                    <div class="wizard-step" data-step="2">
                        <h3>Select Your Plan</h3>
                        <div class="plan-options">
                            <label class="plan-card">
                                <input type="radio" name="plan" value="Starter" required>
                                <span class="plan-name">Starter</span>
                                <span class="plan-price">$29/mo</span>
                                <span class="plan-desc">Perfect for small teams</span>
                            </label>
                            <label class="plan-card popular">
                                <input type="radio" name="plan" value="Professional" checked>
                                <span class="plan-name">Professional</span>
                                <span class="plan-price">$99/mo</span>
                                <span class="plan-desc">Most popular choice</span>
                            </label>
                            <label class="plan-card">
                                <input type="radio" name="plan" value="Enterprise">
                                <span class="plan-name">Enterprise</span>
                                <span class="plan-price">$299/mo</span>
                                <span class="plan-desc">For large organizations</span>
                            </label>
                        </div>
                    </div>

                    <!-- STEP 4: VERIFY  -->
                    <div class="wizard-step" data-step="3">
                        <h3>Review & Create Account</h3>
                        <div class="verify-summary" id="verifySummary"></div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="termsAccepted" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>
                    </div>

                    <div class="wizard-controls">
                        <button type="button" class="btn btn--outline" id="prevStep">Back</button>
                        <button type="button" class="btn btn--primary" id="nextStep">Next</button>
                    </div>
                </form>
            </section>
        </div>

        <!-- DASHBOARD PAGE -->
        <div id="dashboard-page" class="page">
            <div class="dashboard">
                <aside class="dashboard-sidebar" id="dashboardSidebar">
                    <div class="sidebar-header">
                        <div class="logo-container">
                            <div class="logo-icon">
                                <i class="fas fa-stream"></i>
                            </div>
                            <span class="logo-text">Data Reflow</span>
                        </div>
                    </div>
                    <nav class="sidebar-nav">
                        <button class="sidebar-link active" data-section="overview">
                            <i class="fas fa-chart-line"></i> 
                            <span>Overview</span>
                        </button>
                        <button class="sidebar-link" data-section="sources">
                            <i class="fas fa-database"></i> 
                            <span>Data Sources</span>
                        </button>
                        <button class="sidebar-link" data-section="pipelines">
                            <i class="fas fa-project-diagram"></i> 
                            <span>Pipelines</span>
                        </button>
                        <button class="sidebar-link" data-section="mapping">
                            <i class="fas fa-sitemap"></i> 
                            <span>Mapping</span>
                        </button>
                        <button class="sidebar-link" data-section="quality">
                            <i class="fas fa-check-double"></i> 
                            <span>Quality</span>
                        </button>
                        <button class="sidebar-link" data-section="analytics">
                            <i class="fas fa-chart-pie"></i> 
                            <span>Analytics</span>
                        </button>
                        <button class="sidebar-link" data-section="settings">
                            <i class="fas fa-cog"></i> 
                            <span>Settings</span>
                        </button>
                    </nav>
                    <div class="sidebar-footer">
                        <div class="user-info" id="userInfo">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-details">
                                <div class="user-name">Welcome!</div>
                                <div class="user-plan">Professional Plan</div>
                            </div>
                        </div>
                    </div>
                </aside>
                
                <main class="dashboard-main" id="dashboardMain">
                    <button class="mobile-menu-toggle" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <!-- Overview Section -->
                    <section class="dashboard-section active" id="section-overview">
                        <div class="section-header">
                            <h2>Key Metrics</h2>
                            <p>Real-time insights into your data operations</p>
                        </div>
                        <div class="kpi-grid">
                            <div class="kpi-card">
                                <div class="kpi-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="kpi-content">
                                    <h3>Revenue</h3>
                                    <p id="kpiRevenue" class="kpi-value">$245,000</p>
                                    <span class="kpi-change positive">+14%</span>
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="kpi-content">
                                    <h3>Conversion Rate</h3>
                                    <p id="kpiConversion" class="kpi-value">4.5%</p>
                                    <span class="kpi-change positive">+0.4%</span>
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="kpi-content">
                                    <h3>New Customers</h3>
                                    <p id="kpiCustomers" class="kpi-value">310</p>
                                    <span class="kpi-change positive">+18%</span>
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-icon">
                                    <i class="fas fa-heart"></i>
                                </div>
                                <div class="kpi-content">
                                    <h3>Customer LTV</h3>
                                    <p id="kpiLTV" class="kpi-value">$1,084</p>
                                    <span class="kpi-change positive">+7%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-charts">
                            <div class="chart-card">
                                <h3>Revenue Trend</h3>
                                <div class="chart-container">
                                    <canvas id="revenueChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                            <div class="chart-card">
                                <h3>Data Processing Volume</h3>
                                <div class="chart-container">
                                    <canvas id="volumeChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Data Sources Section -->
                    <section class="dashboard-section" id="section-sources">
                        <div class="section-header">
                            <h2>Data Sources</h2>
                            <p>Manage your connected data sources and monitor their status</p>
                            <button class="btn btn--primary">
                                <i class="fas fa-plus"></i>
                                Add Source
                            </button>
                        </div>
                        <div class="sources-grid" id="sourcesGrid">
                            <!-- Sources will be populated by JavaScript -->
                        </div>
                    </section>
                    
                    <!-- Pipelines Section -->
                    <section class="dashboard-section" id="section-pipelines">
                        <div class="section-header">
                            <h2>Data Pipelines</h2>
                            <p>Monitor and manage your ETL/ELT data processing pipelines</p>
                            <button class="btn btn--primary">
                                <i class="fas fa-plus"></i>
                                Create Pipeline
                            </button>
                        </div>
                        <div class="pipelines-list" id="pipelinesList">
                            <!-- Pipelines will be populated by JavaScript -->
                        </div>
                    </section>
                    
                    <!-- Mapping Section -->
                    <section class="dashboard-section" id="section-mapping">
                        <div class="section-header">
                            <h2>Smart Data Mapping</h2>
                            <p>AI-powered field mapping with automatic relationship detection</p>
                        </div>
                        <div class="mapping-container">
                            <div class="mapping-placeholder">
                                <i class="fas fa-sitemap"></i>
                                <h3>Visual Mapping Editor</h3>
                                <p>Interactive data mapping interface coming soon. Experience drag-and-drop field mapping with AI suggestions.</p>
                                <button class="btn btn--primary">Request Beta Access</button>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Quality Section -->
                    <section class="dashboard-section" id="section-quality">
                        <div class="section-header">
                            <h2>Data Quality Monitoring</h2>
                            <p>Real-time quality assessment with automated issue detection</p>
                        </div>
                        <div class="quality-overview">
                            <div class="overall-score">
                                <h3>Overall Quality Score</h3>
                                <div class="score-circle">
                                    <span class="score-value">87%</span>
                                </div>
                                <p class="score-label">Excellent</p>
                            </div>
                        </div>
                        <div class="quality-metrics" id="qualityMetrics">
                            <!-- Quality metrics will be populated by JavaScript -->
                        </div>
                    </section>
                    
                    <!-- Analytics Section -->
                    <section class="dashboard-section" id="section-analytics">
                        <div class="section-header">
                            <h2>Advanced Analytics</h2>
                            <p>Business intelligence and predictive insights from your data</p>
                        </div>
                        <div class="analytics-dashboard">
                            <div class="analytics-card">
                                <h3>Customer Segments</h3>
                                <div class="segment-chart">
                                    <canvas id="segmentChart" width="300" height="300"></canvas>
                                </div>
                            </div>
                            <div class="analytics-card">
                                <h3>Predictive Insights</h3>
                                <div class="insights-list">
                                    <div class="insight-item">
                                        <i class="fas fa-arrow-up text-success"></i>
                                        <span>Revenue likely to increase 12% next quarter</span>
                                    </div>
                                    <div class="insight-item">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                        <span>Customer churn risk detected in segment B</span>
                                    </div>
                                    <div class="insight-item">
                                        <i class="fas fa-lightbulb text-info"></i>
                                        <span>New upselling opportunity identified</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Settings Section -->
                    <section class="dashboard-section" id="section-settings">
                        <div class="section-header">
                            <h2>Account Settings</h2>
                            <p>Manage your account, billing, and preferences</p>
                        </div>
                        <div class="settings-grid">
                            <div class="settings-card">
                                <h3>Profile Information</h3>
                                <div class="form-group">
                                    <label class="form-label">Name</label>
                                    <input type="text" class="form-control" id="profileName" placeholder="Your name">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="profileEmail" placeholder="<EMAIL>">
                                </div>
                                <button class="btn btn--primary">Update Profile</button>
                            </div>
                            <div class="settings-card">
                                <h3>Preferences</h3>
                                <div class="preference-item">
                                    <span>Email Notifications</span>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="preference-item">
                                    <span>SMS Alerts</span>
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <button class="btn btn--outline" id="logoutBtn">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Sign Out
                                </button>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-stream"></i>
                        </div>
                        <span class="logo-text">Data Reflow</span>
                    </div>
                    <p>Transform your data into competitive advantage with our enterprise-grade platform.</p>
                </div>
                <div class="footer-links">
                    <div class="footer-column">
                        <h4>Product</h4>
                        <a href="#" data-page="platform">Features</a>
                        <a href="#" data-page="pricing">Pricing</a>
                        <a href="#" data-page="enterprise">Enterprise</a>
                    </div>
                    <div class="footer-column">
                        <h4>Company</h4>
                        <a href="#">About Us</a>
                        <a href="#">Careers</a>
                        <a href="#">Contact</a>
                    </div>
                    <div class="footer-column">
                        <h4>Support</h4>
                        <a href="#">Documentation</a>
                        <a href="#">Help Center</a>
                        <a href="#">Status</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Data Reflow Platform. All rights reserved.</p>
                <div class="footer-social">
                    <a href="#" target="_blank"><i class="fab fa-twitter"></i></a>
                    <a href="#" target="_blank"><i class="fab fa-linkedin"></i></a>
                    <a href="#" target="_blank"><i class="fab fa-github"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Demo Modal -->
    <div class="modal" id="demoModal">
        <div class="modal-overlay" id="modalOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Data Reflow Platform Demo</h3>
                <button class="modal-close" id="modalClose" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="demo-content">
                    <div class="demo-placeholder">
                        <i class="fas fa-play-circle"></i>
                        <h4>Interactive Product Demo</h4>
                        <p>See how Data Reflow transforms your data processing workflows with real-time demonstrations of our key features.</p>
                        <button class="btn btn--primary">Start Interactive Demo</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div class="modal" id="loginModal">
        <div class="modal-overlay" id="loginOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Sign in to Data Reflow</h3>
                <button class="modal-close" id="loginClose" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label" for="loginEmail">Email</label>
                        <input type="email" id="loginEmail" class="form-control" placeholder="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="loginPassword">Password</label>
                        <input type="password" id="loginPassword" class="form-control" placeholder="••••••••" required>
                    </div>
                    <button type="submit" class="btn btn--primary btn--full-width">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </button>
                    <p class="modal-footer-text">
                        Don't have an account? 
                        <a href="#" id="signupFromLogin">Create one</a>
                    </p>
                </form>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>