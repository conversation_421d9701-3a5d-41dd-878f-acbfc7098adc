:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Enhanced Glassmorphism Design System with Multiple Color Schemes */

/* Color Scheme Variables */
:root {
  /* Default Teal Scheme */
  --scheme-primary: #21808D;
  --scheme-primary-hover: #1D747F;
  --scheme-primary-active: #1A686F;
  --scheme-secondary: #5E5240;
  --scheme-accent: #A84B2F;
  --scheme-background: #FCFCF9;
  --scheme-surface: #FFFFFE;
  --scheme-text: #134252;
  --scheme-text-secondary: #626C71;
  --scheme-border: rgba(94, 82, 64, 0.2);
  --scheme-success: #21808D;
  --scheme-warning: #A84B2F;
  --scheme-error: #C0152F;
  
  /* RGB versions for opacity control */
  --scheme-primary-rgb: 33, 128, 141;
  --scheme-secondary-rgb: 94, 82, 64;
  --scheme-accent-rgb: 168, 75, 47;
}

/* Sunset Energy Theme */
[data-color-scheme="sunset"] {
  --scheme-primary: #6B46C1;
  --scheme-primary-hover: #5B3BA8;
  --scheme-primary-active: #4C3190;
  --scheme-secondary: #FF6B35;
  --scheme-accent: #FF8A80;
  --scheme-background: #F8FAFC;
  --scheme-surface: #FFFFFF;
  --scheme-text: #1F2937;
  --scheme-text-secondary: #6B7280;
  --scheme-border: rgba(255, 107, 53, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 107, 70, 193;
  --scheme-secondary-rgb: 255, 107, 53;
  --scheme-accent-rgb: 255, 138, 128;
}

/* Electric Modern Theme */
[data-color-scheme="electric"] {
  --scheme-primary: #0EA5E9;
  --scheme-primary-hover: #0284C7;
  --scheme-primary-active: #0369A1;
  --scheme-secondary: #10B981;
  --scheme-accent: #FBBF24;
  --scheme-background: #F1F5F9;
  --scheme-surface: #FFFFFF;
  --scheme-text: #0F172A;
  --scheme-text-secondary: #64748B;
  --scheme-border: rgba(16, 185, 129, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 14, 165, 233;
  --scheme-secondary-rgb: 16, 185, 129;
  --scheme-accent-rgb: 251, 191, 36;
}

/* Earthy Professional Theme */
[data-color-scheme="earthy"] {
  --scheme-primary: #059669;
  --scheme-primary-hover: #047857;
  --scheme-primary-active: #065F46;
  --scheme-secondary: #DC6803;
  --scheme-accent: #D97706;
  --scheme-background: #FEF7ED;
  --scheme-surface: #FFFBEB;
  --scheme-text: #451A03;
  --scheme-text-secondary: #92400E;
  --scheme-border: rgba(220, 104, 3, 0.2);
  --scheme-success: #059669;
  --scheme-warning: #D97706;
  --scheme-error: #DC2626;
  
  --scheme-primary-rgb: 5, 150, 105;
  --scheme-secondary-rgb: 220, 104, 3;
  --scheme-accent-rgb: 217, 119, 6;
}

/* Royal Elegance Theme */
[data-color-scheme="royal"] {
  --scheme-primary: #1E3A8A;
  --scheme-primary-hover: #1E40AF;
  --scheme-primary-active: #1D4ED8;
  --scheme-secondary: #EC4899;
  --scheme-accent: #6EE7B7;
  --scheme-background: #FAFAF9;
  --scheme-surface: #FFFFFF;
  --scheme-text: #111827;
  --scheme-text-secondary: #4B5563;
  --scheme-border: rgba(236, 72, 153, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 30, 58, 138;
  --scheme-secondary-rgb: 236, 72, 153;
  --scheme-accent-rgb: 110, 231, 183;
}

/* Vibrant Tech Theme */
[data-color-scheme="vibrant"] {
  --scheme-primary: #DB2777;
  --scheme-primary-hover: #BE185D;
  --scheme-primary-active: #9D174D;
  --scheme-secondary: #06B6D4;
  --scheme-accent: #84CC16;
  --scheme-background: #F8FAFC;
  --scheme-surface: #FFFFFF;
  --scheme-text: #030712;
  --scheme-text-secondary: #374151;
  --scheme-border: rgba(6, 182, 212, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 219, 39, 119;
  --scheme-secondary-rgb: 6, 182, 212;
  --scheme-accent-rgb: 132, 204, 22;
}

/* Dark Mode Variants */
[data-color-scheme="default"][data-theme="dark"] {
  --scheme-background: #1F2121;
  --scheme-surface: #262828;
  --scheme-text: #F5F5F5;
  --scheme-text-secondary: rgba(167, 169, 169, 0.7);
  --scheme-border: rgba(119, 124, 124, 0.3);
  --scheme-primary: #32B8C6;
}

[data-color-scheme="sunset"][data-theme="dark"] {
  --scheme-background: #1A1625;
  --scheme-surface: #241B33;
  --scheme-text: #F8FAFC;
  --scheme-text-secondary: #94A3B8;
  --scheme-primary: #8B5FD6;
  --scheme-secondary: #FF7A47;
}

[data-color-scheme="electric"][data-theme="dark"] {
  --scheme-background: #0F1419;
  --scheme-surface: #1E293B;
  --scheme-text: #F1F5F9;
  --scheme-text-secondary: #94A3B8;
  --scheme-primary: #38BDF8;
  --scheme-secondary: #34D399;
}

[data-color-scheme="earthy"][data-theme="dark"] {
  --scheme-background: #1C1917;
  --scheme-surface: #292524;
  --scheme-text: #FEF7ED;
  --scheme-text-secondary: #D6D3D1;
  --scheme-primary: #10B981;
  --scheme-secondary: #FB923C;
}

[data-color-scheme="royal"][data-theme="dark"] {
  --scheme-background: #111827;
  --scheme-surface: #1F2937;
  --scheme-text: #F9FAFB;
  --scheme-text-secondary: #D1D5DB;
  --scheme-primary: #3B82F6;
  --scheme-secondary: #F472B6;
}

[data-color-scheme="vibrant"][data-theme="dark"] {
  --scheme-background: #0F0A14;
  --scheme-surface: #1F1B24;
  --scheme-text: #F8FAFC;
  --scheme-text-secondary: #CBD5E1;
  --scheme-primary: #F472B6;
  --scheme-secondary: #22D3EE;
}

/* Apply color scheme variables to design system */
:root {
  --color-primary: var(--scheme-primary);
  --color-primary-hover: var(--scheme-primary-hover);
  --color-primary-active: var(--scheme-primary-active);
  --color-secondary: var(--scheme-secondary);
  --color-background: var(--scheme-background);
  --color-surface: var(--scheme-surface);
  --color-text: var(--scheme-text);
  --color-text-secondary: var(--scheme-text-secondary);
  --color-border: var(--scheme-border);
  --color-success: var(--scheme-success);
  --color-warning: var(--scheme-warning);
  --color-error: var(--scheme-error);
  --color-primary-rgb: var(--scheme-primary-rgb);
  --color-white: #FFFFFF;
  
  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);
}

/* Base Styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  background: var(--color-background);
  transition: background-color 0.5s var(--ease-standard);
  min-height: 100vh;
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* Page System */
.page-container {
  min-height: 100vh;
  padding-top: 80px;
}

.page {
  display: none;
}

.page.active {
  display: block;
}

/* Glassmorphism Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 0 0 20px rgba(var(--color-primary-rgb), 0); }
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(60px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-60px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(60px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(200%); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Color Scheme Selector Styles */
.theme-controls {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.color-scheme-selector {
  position: relative;
}

.color-scheme-toggle, .theme-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-10);
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
  backdrop-filter: blur(10px);
}

.color-scheme-toggle:hover, .theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-primary);
  transform: scale(1.05);
}

.color-scheme-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: rgba(var(--color-surface), 0.95);
  backdrop-filter: blur(30px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  padding: var(--space-12);
  min-width: 200px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--duration-normal) var(--ease-standard);
}

.color-scheme-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.color-scheme-option {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-10) var(--space-12);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  margin-bottom: var(--space-4);
}

.color-scheme-option:last-child {
  margin-bottom: 0;
}

.color-scheme-option:hover {
  background: rgba(var(--color-primary-rgb), 0.1);
  transform: translateX(4px);
}

.color-scheme-option.active {
  background: rgba(var(--color-primary-rgb), 0.15);
  border: 1px solid rgba(var(--color-primary-rgb), 0.3);
}

.scheme-preview {
  display: flex;
  gap: var(--space-4);
}

.color-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scheme-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Enhanced Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--duration-normal) var(--ease-standard);
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.05);
}

.navbar.scrolled {
  background: rgba(var(--color-surface), 0.95);
  backdrop-filter: blur(30px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-16) 0;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.logo-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-lg);
  box-shadow: 0 8px 24px rgba(var(--color-primary-rgb), 0.3);
  position: relative;
  transition: all 0.5s var(--ease-standard);
}

.logo-icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-md);
  z-index: -1;
  opacity: 0.3;
  filter: blur(8px);
  transition: all 0.5s var(--ease-standard);
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  transition: color 0.5s var(--ease-standard);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-32);
}

.nav-link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all var(--duration-fast) var(--ease-standard);
  position: relative;
  padding: var(--space-8) 0;
  cursor: pointer;
}

.nav-link:hover, .nav-link.active {
  color: var(--color-primary);
  transform: translateY(-2px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--scheme-secondary));
  transition: width var(--duration-fast) var(--ease-standard);
  border-radius: 2px;
}

.nav-link:hover::after, .nav-link.active::after {
  width: 100%;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-8);
}

.menu-toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
  border-radius: 2px;
}

/* Container */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
  max-width: 1280px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-md); }

p {
  margin: 0 0 var(--space-16) 0;
  color: var(--color-text);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
  gap: var(--space-8);
}

.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.4);
}

.btn--primary {
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  color: var(--color-white);
  border: none;
}

.btn--primary:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--scheme-accent));
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary-rgb), 0.4);
}

.btn--outline {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.btn--outline:hover {
  background: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form controls */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: all 0.3s var(--ease-standard);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
  outline: none;
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Hero Section */
.hero {
  padding: 120px 0;
  background: linear-gradient(135deg, 
    rgba(var(--scheme-primary-rgb), 0.05) 0%, 
    rgba(var(--scheme-secondary-rgb), 0.05) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(var(--color-primary-rgb), 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  margin-bottom: var(--space-24);
  background: linear-gradient(135deg, var(--color-text), var(--color-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-32);
  line-height: 1.6;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.stat {
  text-align: center;
  padding: var(--space-16);
  background: rgba(var(--color-surface), 0.6);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.hero-cta {
  display: flex;
  gap: var(--space-16);
}

.hero-visual {
  position: relative;
}

.dashboard-preview {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  transform: perspective(1000px) rotateY(-15deg) rotateX(10deg);
  transition: all 0.6s ease;
}

.dashboard-preview:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-12) var(--space-16);
  background: rgba(var(--color-primary-rgb), 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-controls {
  display: flex;
  gap: var(--space-8);
}

.control-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control-dot.red { background: #FF5F56; }
.control-dot.yellow { background: #FFBD2E; }
.control-dot.green { background: #27CA3F; }

.preview-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.preview-content {
  display: flex;
  height: 300px;
}

.preview-sidebar {
  width: 200px;
  background: rgba(var(--color-surface), 0.5);
  padding: var(--space-16);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-8) var(--space-12);
  border-radius: var(--radius-base);
  margin-bottom: var(--space-8);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.sidebar-item.active {
  background: var(--color-primary);
  color: var(--color-white);
}

.preview-main {
  flex: 1;
  padding: var(--space-16);
}

.preview-cards {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-24);
}

.preview-card {
  flex: 1;
  padding: var(--space-16);
  background: rgba(var(--color-surface), 0.8);
  border-radius: var(--radius-base);
  text-align: center;
}

.card-metric {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.card-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.preview-chart {
  height: 120px;
  background: rgba(var(--color-surface), 0.5);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  display: flex;
  align-items: end;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: var(--space-8);
  width: 100%;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--color-primary), var(--scheme-secondary));
  border-radius: 2px 2px 0 0;
  min-height: 20px;
  animation: slideInUp 0.6s ease-out;
}

/* Sections */
.features, .testimonials, .pricing-preview {
  padding: 80px 0;
}

.section-header {
  text-align: center;
  max-width: 600px;
  margin: 0 auto var(--space-32);
}

.section-header h2 {
  margin-bottom: var(--space-16);
}

.section-header p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-32);
}

.feature-card {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-standard);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-24);
  font-size: var(--font-size-2xl);
  color: var(--color-white);
}

.feature-title {
  margin-bottom: var(--space-16);
}

.feature-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-24);
}

.feature-benefits {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-benefits li {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-4) 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.feature-benefits li::before {
  content: '✓';
  color: var(--color-success);
  font-weight: bold;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-32);
}

.testimonial-card {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  transition: all var(--duration-normal) var(--ease-standard);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.testimonial-quote {
  font-style: italic;
  font-size: var(--font-size-lg);
  line-height: 1.6;
  margin-bottom: var(--space-24);
  color: var(--color-text);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-weight: bold;
  font-size: var(--font-size-lg);
}

.author-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.author-title {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.testimonial-rating {
  margin-top: var(--space-8);
}

.star {
  color: #FFD700;
}

/* Page Hero */
.page-hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, 
    rgba(var(--scheme-primary-rgb), 0.05) 0%, 
    rgba(var(--scheme-secondary-rgb), 0.05) 100%);
  text-align: center;
}

.page-hero-content h1 {
  font-size: 3rem;
  margin-bottom: var(--space-16);
}

.page-hero-content p {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.page-content {
  padding: 80px 0;
}

/* Pricing */
.pricing-cta {
  text-align: center;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-32);
}

.solution-card, .enterprise-feature {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-standard);
}

.solution-card:hover, .enterprise-feature:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.solution-icon, .enterprise-feature i {
  font-size: 3rem;
  color: var(--color-primary);
  margin-bottom: var(--space-16);
}

.enterprise-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-32);
}

/* Signup Wizard Styles */
.signup-wizard {
  max-width: 600px;
  margin: var(--space-32) auto;
  padding: var(--space-32);
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.wizard-title {
  text-align: center;
  margin-bottom: var(--space-16);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

.wizard-sub {
  text-align: center;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-32);
  font-size: var(--font-size-lg);
}

.wizard-progress {
  width: 100%;
  height: 4px;
  background: var(--color-border);
  border-radius: var(--radius-full);
  margin-bottom: var(--space-32);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-full);
  transition: width 0.3s var(--ease-standard);
  width: 25%;
}

.wizard-step {
  display: none;
}

.wizard-step.active {
  display: block;
  animation: slideInRight 0.3s ease-out;
}

.wizard-step h3 {
  margin-bottom: var(--space-24);
  text-align: center;
}

.plan-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.plan-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-20);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  position: relative;
  text-align: center;
}

.plan-card input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.plan-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(var(--color-primary-rgb), 0.15);
}

.plan-card input:checked ~ .plan-name,
.plan-card input:checked ~ .plan-price {
  color: var(--color-primary);
}

.plan-card.popular {
  border-color: var(--color-primary);
  position: relative;
}

.plan-card.popular::before {
  content: 'Popular';
  position: absolute;
  top: -12px;
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
}

.plan-name {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-8);
}

.plan-price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

.plan-desc {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.verify-summary {
  background: rgba(var(--color-primary-rgb), 0.1);
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
  border-radius: var(--radius-base);
  padding: var(--space-20);
  margin-bottom: var(--space-24);
  font-size: var(--font-size-base);
  line-height: 1.6;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.checkbox-label input {
  width: 20px;
  height: 20px;
}

.wizard-controls {
  display: flex;
  justify-content: space-between;
  gap: var(--space-16);
}

/* Dashboard Styles */
.dashboard {
  display: flex;
  min-height: calc(100vh - 80px);
  background: var(--color-background);
}

.dashboard-sidebar {
  width: 280px;
  background: rgba(var(--color-surface), 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--color-border);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: var(--space-24) var(--space-20);
  border-bottom: 1px solid var(--color-border);
}

.sidebar-nav {
  flex: 1;
  padding: var(--space-20);
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-12) var(--space-16);
  border: none;
  background: none;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
  text-align: left;
  width: 100%;
}

.sidebar-link:hover {
  background: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
  transform: translateX(4px);
}

.sidebar-link.active {
  background: var(--color-primary);
  color: var(--color-white);
}

.sidebar-link i {
  width: 20px;
  text-align: center;
}

.sidebar-footer {
  padding: var(--space-20);
  border-top: 1px solid var(--color-border);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
}

.user-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

.user-plan {
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
}

.dashboard-main {
  flex: 1;
  padding: var(--space-32);
  overflow-y: auto;
  position: relative;
}

.mobile-menu-toggle {
  display: none;
  position: absolute;
  top: var(--space-20);
  left: var(--space-20);
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-base);
  padding: var(--space-8);
  cursor: pointer;
  z-index: 100;
}

.dashboard-section {
  display: none;
}

.dashboard-section.active {
  display: block;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-32);
}

.section-header div {
  flex: 1;
}

.section-header h2 {
  margin-bottom: var(--space-8);
  color: var(--color-text);
}

.section-header p {
  color: var(--color-text-secondary);
  margin: 0;
}

/* KPI Cards */
.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.kpi-card {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  display: flex;
  align-items: center;
  gap: var(--space-16);
  transition: all var(--duration-normal) var(--ease-standard);
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.kpi-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-xl);
}

.kpi-content h3 {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.kpi-change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.kpi-change.positive {
  color: var(--color-success);
}

.kpi-change.negative {
  color: var(--color-error);
}

.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-24);
}

.chart-card {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.chart-card h3 {
  margin-bottom: var(--space-16);
  color: var(--color-text);
}

.chart-container {
  width: 100%;
  height: 200px;
  position: relative;
}

/* Sources Grid */
.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
}

.source-card {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: all var(--duration-normal) var(--ease-standard);
}

.source-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

/* Pipelines List */
.pipelines-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.pipeline-item {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--duration-fast) var(--ease-standard);
}

.pipeline-item:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.pipeline-info h4 {
  margin-bottom: var(--space-4);
  color: var(--color-text);
}

.pipeline-info p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

/* Quality Section */
.quality-overview {
  text-align: center;
  margin-bottom: var(--space-32);
}

.overall-score h3 {
  margin-bottom: var(--space-16);
  color: var(--color-text);
}

.score-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: conic-gradient(var(--color-primary) 0deg, var(--color-primary) 312deg, var(--color-border) 312deg, var(--color-border) 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-16);
  position: relative;
}

.score-circle::before {
  content: '';
  width: 100px;
  height: 100px;
  background: var(--color-background);
  border-radius: 50%;
  position: absolute;
}

.score-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  position: relative;
  z-index: 1;
}

.score-label {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.quality-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-24);
}

.quality-metric {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  text-align: center;
}

/* Analytics */
.analytics-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-24);
}

.analytics-card {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.insight-item {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-12);
  background: rgba(var(--color-primary-rgb), 0.05);
  border-radius: var(--radius-base);
}

.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-info { color: var(--color-primary); }

/* Settings */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-24);
}

.settings-card {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.preference-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-12) 0;
  border-bottom: 1px solid var(--color-border);
}

.preference-item:last-child {
  border-bottom: none;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-border);
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: var(--color-white);
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--color-primary);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Mapping placeholder */
.mapping-container, .mapping-placeholder {
  text-align: center;
  padding: var(--space-32);
}

.mapping-placeholder {
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-lg);
}

.mapping-placeholder i {
  font-size: 4rem;
  color: var(--color-primary);
  margin-bottom: var(--space-16);
}

/* Footer */
.footer {
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
  padding: var(--space-32) 0 var(--space-24);
  transition: background-color 0.5s var(--ease-standard);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-32);
  margin-bottom: var(--space-24);
}

.footer-brand p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-16);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-32);
}

.footer-column h4 {
  margin-bottom: var(--space-16);
  color: var(--color-text);
}

.footer-column a {
  display: block;
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: var(--space-4) 0;
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer-column a:hover {
  color: var(--color-primary);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-24);
  border-top: 1px solid var(--color-border);
}

.footer-bottom p {
  color: var(--color-text-secondary);
  margin: 0;
}

.footer-social {
  display: flex;
  gap: var(--space-16);
}

.footer-social a {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer-social a:hover {
  color: var(--color-primary);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  max-width: 600px;
  margin: 5% auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.modal.active .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: var(--space-24);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: var(--color-text);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-8);
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
}

.modal-close:hover {
  background: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-text);
}

.modal-body {
  padding: var(--space-32);
}

.modal-footer-text {
  text-align: center;
  margin-top: var(--space-16);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.modal-footer-text a {
  color: var(--color-primary);
  text-decoration: none;
}

.demo-placeholder {
  text-align: center;
  padding: var(--space-32);
}

.demo-placeholder i {
  font-size: 4rem;
  color: var(--color-primary);
  margin-bottom: var(--space-16);
}

.demo-placeholder h4 {
  color: var(--color-text);
  margin-bottom: var(--space-12);
}

.demo-placeholder p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-24);
}

/* Utility classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-container {
    padding-top: 60px;
  }
  
  .nav-content {
    padding: var(--space-12) 0;
  }
  
  .color-scheme-dropdown {
    right: auto;
    left: 0;
    min-width: 180px;
  }
  
  .theme-controls {
    gap: var(--space-4);
  }
  
  .nav-menu {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background: rgba(var(--color-surface), 0.95);
    backdrop-filter: blur(30px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-direction: column;
    padding: var(--space-20);
    gap: var(--space-16);
    transform: translateY(-100%);
    transition: transform var(--duration-normal) var(--ease-standard);
    opacity: 0;
    pointer-events: none;
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    pointer-events: all;
  }

  .menu-toggle {
    display: flex;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .dashboard {
    flex-direction: column;
  }

  .dashboard-sidebar {
    width: 100%;
    position: fixed;
    top: 80px;
    left: -100%;
    height: calc(100vh - 80px);
    z-index: 999;
    transition: left var(--duration-normal) var(--ease-standard);
  }

  .dashboard-sidebar.active {
    left: 0;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }

  .dashboard-main {
    padding: var(--space-16);
    padding-top: 60px;
  }

  .kpi-grid, .dashboard-charts, .sources-grid, .quality-metrics, .analytics-dashboard, .settings-grid {
    grid-template-columns: 1fr;
  }

  .wizard-controls {
    flex-direction: column;
  }

  .plan-options {
    grid-template-columns: 1fr;
  }
  
  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .modal-content {
    margin: 10% var(--space-16);
    max-width: none;
  }
  
  .footer-content, .footer-links {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--space-16);
    text-align: center;
  }
}

/* Color scheme transition animations */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: 0.5s;
  transition-timing-function: var(--ease-standard);
}

/* Ensure smooth transitions for all themed elements */
.logo-icon, .logo-icon::before, .progress-bar, .score-circle {
  transition: all 0.8s var(--ease-standard) !important;
}

/* Status colors */
.status-active, .status-success {
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
}

.status-error, .status-failed {
  color: var(--color-error);
  font-weight: var(--font-weight-medium);
}

.status-running, .status-pending {
  color: var(--color-warning);
  font-weight: var(--font-weight-medium);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2') format('woff2');
}