SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ar_internal_metadata; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ar_internal_metadata (
    key character varying NOT NULL,
    value character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: data_sources; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_sources (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    name character varying NOT NULL,
    description text,
    source_type character varying NOT NULL,
    connection_config text,
    settings jsonb DEFAULT '{}'::jsonb,
    status character varying DEFAULT 'inactive'::character varying NOT NULL,
    active boolean DEFAULT false NOT NULL,
    error_message text,
    last_error_at timestamp(6) without time zone,
    sync_frequency character varying DEFAULT 'manual'::character varying,
    last_sync_at timestamp(6) without time zone,
    last_successful_sync_at timestamp(6) without time zone,
    next_sync_at timestamp(6) without time zone,
    sync_count integer DEFAULT 0,
    failed_sync_count integer DEFAULT 0,
    row_count bigint DEFAULT 0,
    total_rows_synced bigint DEFAULT 0,
    data_size_mb numeric(10,2),
    last_sync_duration_seconds integer,
    avg_sync_duration_seconds numeric(10,2),
    metadata jsonb DEFAULT '{}'::jsonb,
    schema_cache jsonb,
    schema_updated_at timestamp(6) without time zone,
    original_filename character varying,
    file_size bigint,
    file_uploaded_at timestamp(6) without time zone,
    version integer DEFAULT 1,
    created_by character varying,
    updated_by character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    CONSTRAINT check_data_source_status CHECK (((status)::text = ANY ((ARRAY['inactive'::character varying, 'active'::character varying, 'syncing'::character varying, 'error'::character varying, 'maintenance'::character varying, 'pending'::character varying])::text[]))),
    CONSTRAINT check_data_source_type CHECK (((source_type)::text = ANY ((ARRAY['postgresql'::character varying, 'mysql'::character varying, 'sqlite'::character varying, 'sqlserver'::character varying, 'mongodb'::character varying, 'redis'::character varying, 'elasticsearch'::character varying, 'csv'::character varying, 'excel'::character varying, 'json'::character varying, 'xml'::character varying, 'api'::character varying, 'webhook'::character varying, 'stream'::character varying, 's3'::character varying, 'gcs'::character varying, 'azure_blob'::character varying, 'sftp'::character varying, 'quickbooks'::character varying, 'stripe'::character varying, 'shopify'::character varying, 'salesforce'::character varying])::text[]))),
    CONSTRAINT check_sync_frequency CHECK (((sync_frequency)::text = ANY ((ARRAY['manual'::character varying, 'realtime'::character varying, 'every_5_minutes'::character varying, 'every_15_minutes'::character varying, 'every_30_minutes'::character varying, 'hourly'::character varying, 'every_6_hours'::character varying, 'daily'::character varying, 'weekly'::character varying, 'monthly'::character varying])::text[])))
);


--
-- Name: data_sources_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.data_sources_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: data_sources_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.data_sources_id_seq OWNED BY public.data_sources.id;


--
-- Name: integrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.integrations (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint NOT NULL,
    name character varying NOT NULL,
    sync_type character varying,
    mapping json,
    transformation_rules json,
    target_table character varying,
    status character varying DEFAULT 'inactive'::character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: integrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.integrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: integrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.integrations_id_seq OWNED BY public.integrations.id;


--
-- Name: organizations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.organizations (
    id bigint NOT NULL,
    name character varying NOT NULL,
    plan character varying DEFAULT 'starter'::character varying,
    settings text,
    subdomain character varying,
    monthly_row_limit integer,
    current_month_rows integer DEFAULT 0,
    plan_expires_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: organizations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.organizations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: organizations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.organizations_id_seq OWNED BY public.organizations.id;


--
-- Name: pipelines; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pipelines (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint NOT NULL,
    name character varying NOT NULL,
    description text,
    schedule character varying,
    status character varying DEFAULT 'inactive'::character varying NOT NULL,
    configuration json,
    last_run_at timestamp(6) without time zone,
    error_message text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: pipelines_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.pipelines_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pipelines_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.pipelines_id_seq OWNED BY public.pipelines.id;


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version character varying NOT NULL
);


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    email character varying NOT NULL,
    encrypted_password character varying NOT NULL,
    role character varying DEFAULT 'member'::character varying,
    first_name character varying,
    last_name character varying,
    last_sign_in_at timestamp(6) without time zone,
    active boolean DEFAULT true,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    reset_password_token character varying,
    reset_password_sent_at timestamp(6) without time zone,
    remember_created_at timestamp(6) without time zone,
    sign_in_count integer DEFAULT 0 NOT NULL,
    current_sign_in_at timestamp(6) without time zone,
    current_sign_in_ip character varying,
    last_sign_in_ip character varying
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: data_sources id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_sources ALTER COLUMN id SET DEFAULT nextval('public.data_sources_id_seq'::regclass);


--
-- Name: integrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations ALTER COLUMN id SET DEFAULT nextval('public.integrations_id_seq'::regclass);


--
-- Name: organizations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations ALTER COLUMN id SET DEFAULT nextval('public.organizations_id_seq'::regclass);


--
-- Name: pipelines id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines ALTER COLUMN id SET DEFAULT nextval('public.pipelines_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: ar_internal_metadata ar_internal_metadata_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ar_internal_metadata
    ADD CONSTRAINT ar_internal_metadata_pkey PRIMARY KEY (key);


--
-- Name: data_sources data_sources_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_sources
    ADD CONSTRAINT data_sources_pkey PRIMARY KEY (id);


--
-- Name: integrations integrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT integrations_pkey PRIMARY KEY (id);


--
-- Name: organizations organizations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_pkey PRIMARY KEY (id);


--
-- Name: pipelines pipelines_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines
    ADD CONSTRAINT pipelines_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: index_data_sources_on_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_active ON public.data_sources USING btree (active);


--
-- Name: index_data_sources_on_last_sync_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_last_sync_at ON public.data_sources USING btree (last_sync_at);


--
-- Name: index_data_sources_on_next_sync_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_next_sync_at ON public.data_sources USING btree (next_sync_at);


--
-- Name: index_data_sources_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_organization_id ON public.data_sources USING btree (organization_id);


--
-- Name: index_data_sources_on_organization_id_and_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_organization_id_and_active ON public.data_sources USING btree (organization_id, active);


--
-- Name: index_data_sources_on_organization_id_and_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_data_sources_on_organization_id_and_name ON public.data_sources USING btree (organization_id, name);


--
-- Name: index_data_sources_on_organization_id_and_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_organization_id_and_status ON public.data_sources USING btree (organization_id, status);


--
-- Name: index_data_sources_on_source_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_source_type ON public.data_sources USING btree (source_type);


--
-- Name: index_data_sources_on_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_status ON public.data_sources USING btree (status);


--
-- Name: index_integrations_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integrations_on_data_source_id ON public.integrations USING btree (data_source_id);


--
-- Name: index_integrations_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integrations_on_organization_id ON public.integrations USING btree (organization_id);


--
-- Name: index_integrations_on_organization_id_and_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_integrations_on_organization_id_and_name ON public.integrations USING btree (organization_id, name);


--
-- Name: index_organizations_on_plan; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_organizations_on_plan ON public.organizations USING btree (plan);


--
-- Name: index_organizations_on_subdomain; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_organizations_on_subdomain ON public.organizations USING btree (subdomain);


--
-- Name: index_pipelines_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pipelines_on_data_source_id ON public.pipelines USING btree (data_source_id);


--
-- Name: index_pipelines_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pipelines_on_organization_id ON public.pipelines USING btree (organization_id);


--
-- Name: index_pipelines_on_organization_id_and_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_pipelines_on_organization_id_and_name ON public.pipelines USING btree (organization_id, name);


--
-- Name: index_users_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_users_on_organization_id ON public.users USING btree (organization_id);


--
-- Name: index_users_on_organization_id_and_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_organization_id_and_email ON public.users USING btree (organization_id, email);


--
-- Name: index_users_on_reset_password_token; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_reset_password_token ON public.users USING btree (reset_password_token);


--
-- Name: index_users_on_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_users_on_role ON public.users USING btree (role);


--
-- Name: integrations fk_rails_755d734f25; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT fk_rails_755d734f25 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: integrations fk_rails_81772e8cd9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT fk_rails_81772e8cd9 FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: data_sources fk_rails_99f4fec2c8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_sources
    ADD CONSTRAINT fk_rails_99f4fec2c8 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: pipelines fk_rails_bdf19edfed; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines
    ADD CONSTRAINT fk_rails_bdf19edfed FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: pipelines fk_rails_c6460bbd7f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines
    ADD CONSTRAINT fk_rails_c6460bbd7f FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: users fk_rails_d7b9ff90af; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT fk_rails_d7b9ff90af FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- PostgreSQL database dump complete
--

SET search_path TO "$user", public;

INSERT INTO "schema_migrations" (version) VALUES
('20250720223045'),
('20250720223036'),
('20250720214000'),
('20250720163902'),
('20250720162902'),
('20250720162820');

