FactoryBot.define do
  factory :data_source do
    organization
    sequence(:name) { |n| "Data Source #{n}" }
    source_type { 'postgresql' }
    status { 'inactive' }
    sync_frequency { 'manual' }
    connection_config { { host: 'localhost', port: 5432, database: 'test_db', username: 'test_user' }.to_json }
    
    trait :active do
      status { 'active' }
    end
    
    trait :with_error do
      status { 'error' }
      error_message { 'Connection refused' }
    end
    
    trait :syncing do
      status { 'syncing' }
    end
    
    trait :with_data do
      row_count { 10000 }
      last_sync_at { 1.hour.ago }
      metadata { { tables: ['users', 'orders'], last_sync_duration: 45 } }
    end
    
    trait :mysql do
      source_type { 'mysql' }
    end
    
    trait :csv do
      source_type { 'csv' }
      connection_config { { file_path: '/tmp/data.csv' }.to_json }
    end
    
    trait :api do
      source_type { 'api' }
      connection_config { { endpoint: 'https://api.example.com', api_key: 'secret' }.to_json }
    end
  end
end