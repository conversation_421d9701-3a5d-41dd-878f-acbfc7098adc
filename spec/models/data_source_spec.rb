require 'rails_helper'

RSpec.describe DataSource, type: :model do
  let(:organization) { create(:organization) }
  let(:data_source) { build(:data_source, organization: organization) }
  
  describe 'associations' do
    it { should belong_to(:organization) }
    it { should have_many(:integrations).dependent(:destroy) }
    it { should have_many(:pipelines).dependent(:destroy) }
  end
  
  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:source_type) }
    it { should validate_presence_of(:status) }
    
    it 'validates uniqueness of name within organization' do
      create(:data_source, name: 'Test Source', organization: organization)
      duplicate = build(:data_source, name: 'Test Source', organization: organization)
      expect(duplicate).not_to be_valid
    end
    
    it 'allows same name in different organizations' do
      create(:data_source, name: 'Test Source', organization: organization)
      other_org = create(:organization)
      duplicate = build(:data_source, name: 'Test Source', organization: other_org)
      expect(duplicate).to be_valid
    end
    
    it { should validate_inclusion_of(:source_type).in_array(DataSource::SOURCE_TYPES) }
    it { should validate_inclusion_of(:status).in_array(DataSource::STATUSES) }
    it { should validate_inclusion_of(:sync_frequency).in_array(DataSource::SYNC_FREQUENCIES).allow_nil }
  end
  
  describe 'scopes' do
    let!(:active_source) { create(:data_source, status: 'active', last_sync_at: 30.minutes.ago, organization: organization) }
    let!(:inactive_source) { create(:data_source, status: 'inactive', organization: organization) }
    let!(:error_source) { create(:data_source, status: 'error', organization: organization) }
    let!(:old_sync_source) { create(:data_source, status: 'active', last_sync_at: 2.hours.ago, organization: organization) }
    let!(:never_synced_source) { create(:data_source, status: 'active', last_sync_at: nil, organization: organization) }
    
    describe '.active' do
      it 'returns only active sources' do
        expect(DataSource.active).to contain_exactly(active_source, old_sync_source, never_synced_source)
      end
    end
    
    describe '.inactive' do
      it 'returns only inactive sources' do
        expect(DataSource.inactive).to contain_exactly(inactive_source)
      end
    end
    
    describe '.with_errors' do
      it 'returns only sources with errors' do
        expect(DataSource.with_errors).to contain_exactly(error_source)
      end
    end
    
    describe '.needs_sync' do
      it 'returns active sources that need syncing' do
        expect(DataSource.needs_sync).to contain_exactly(old_sync_source, never_synced_source)
      end
    end
  end
  
  describe 'instance methods' do
    describe '#active?' do
      it 'returns true when status is active' do
        data_source.status = 'active'
        expect(data_source.active?).to be true
      end
      
      it 'returns false when status is not active' do
        data_source.status = 'inactive'
        expect(data_source.active?).to be false
      end
    end
    
    describe '#can_sync?' do
      it 'returns true when active and not syncing' do
        data_source.status = 'active'
        expect(data_source.can_sync?).to be true
      end
      
      it 'returns false when inactive' do
        data_source.status = 'inactive'
        expect(data_source.can_sync?).to be false
      end
      
      it 'returns false when syncing' do
        data_source.status = 'syncing'
        expect(data_source.can_sync?).to be false
      end
    end
    
    describe '#database_source?' do
      it 'returns true for database types' do
        %w[postgresql mysql sqlite].each do |type|
          data_source.source_type = type
          expect(data_source.database_source?).to be true
        end
      end
      
      it 'returns false for non-database types' do
        data_source.source_type = 'csv'
        expect(data_source.database_source?).to be false
      end
    end
    
    describe '#connection_settings' do
      it 'returns parsed JSON settings' do
        settings = { host: 'localhost', port: 5432 }
        data_source.connection_config = settings.to_json
        expect(data_source.connection_settings).to eq(settings.stringify_keys)
      end
      
      it 'returns empty hash for blank config' do
        data_source.connection_config = nil
        expect(data_source.connection_settings).to eq({})
      end
      
      it 'returns empty hash for invalid JSON' do
        data_source.connection_config = 'invalid json'
        expect(data_source.connection_settings).to eq({})
      end
    end
    
    describe '#update_connection_settings' do
      it 'updates connection config with JSON' do
        settings = { host: 'localhost', port: 5432 }
        data_source.save!
        data_source.update_connection_settings(settings)
        expect(data_source.reload.connection_settings).to eq(settings.stringify_keys)
      end
    end
    
    describe '#formatted_row_count' do
      it 'returns formatted number with delimiter' do
        data_source.row_count = 1234567
        expect(data_source.formatted_row_count).to eq('1,234,567')
      end
      
      it 'returns "No data" for zero rows' do
        data_source.row_count = 0
        expect(data_source.formatted_row_count).to eq('No data')
      end
      
      it 'returns "No data" for nil rows' do
        data_source.row_count = nil
        expect(data_source.formatted_row_count).to eq('No data')
      end
    end
    
    describe '#source_type_text' do
      it 'returns human-readable source type' do
        data_source.source_type = 'postgresql'
        expect(data_source.source_type_text).to eq('PostgreSQL')
        
        data_source.source_type = 'csv'
        expect(data_source.source_type_text).to eq('CSV File')
      end
    end
  end
  
  describe 'callbacks' do
    describe 'before_validation' do
      it 'normalizes name by stripping whitespace' do
        data_source.name = '  Test Source  '
        data_source.valid?
        expect(data_source.name).to eq('Test Source')
      end
    end
    
    describe 'after_update' do
      it 'clears error message when status changes from error' do
        data_source.status = 'error'
        data_source.error_message = 'Connection failed'
        data_source.save!
        
        data_source.update!(status: 'active')
        expect(data_source.error_message).to be_nil
      end
      
      it 'keeps error message when status remains error' do
        data_source.status = 'error'
        data_source.error_message = 'Connection failed'
        data_source.save!
        
        data_source.update!(name: 'New Name')
        expect(data_source.error_message).to eq('Connection failed')
      end
    end
  end
  
  describe 'encryption' do
    it 'encrypts connection_config' do
      settings = { password: 'secret123' }.to_json
      data_source.connection_config = settings
      data_source.save!
      
      # The encrypted value should not equal the plain text
      expect(data_source.connection_config_before_type_cast).not_to eq(settings)
      
      # But we should be able to read it back
      expect(data_source.connection_config).to eq(settings)
    end
  end
end