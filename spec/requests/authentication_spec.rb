require 'rails_helper'

RSpec.describe "Authentication", type: :request do
  describe "GET /" do
    context "when not signed in" do
      it "shows the landing page" do
        get root_path
        expect(response).to have_http_status(:success)
        expect(response.body).to include("Data integration")
        expect(response.body).to include("Sign In")
      end
    end

    context "when signed in" do
      let(:organization) { Organization.create!(name: 'Test Org') }
      let(:user) { User.create!(email: '<EMAIL>', password: 'password123', organization: organization) }
      
      before { sign_in user }
      
      it "shows the dashboard" do
        get root_path
        expect(response).to have_http_status(:success)
        expect(response.body).to include("Welcome to DataRefinery")
        expect(response.body).to include(organization.name)
      end
    end
  end

  describe "GET /users/sign_in" do
    it "shows the sign in page" do
      get new_user_session_path
      expect(response).to have_http_status(:success)
      expect(response.body).to include("Sign in to DataRefinery")
    end
  end

  describe "POST /users/sign_in" do
    let(:organization) { Organization.create!(name: 'Test Org') }
    let!(:user) { User.create!(email: '<EMAIL>', password: 'password123', organization: organization) }

    context "with valid credentials" do
      it "signs in the user" do
        post user_session_path, params: {
          user: { email: '<EMAIL>', password: 'password123' }
        }
        expect(response).to redirect_to(root_path)
        follow_redirect!
        expect(response.body).to include("Welcome to DataRefinery")
      end
    end

    context "with invalid credentials" do
      it "shows error message" do
        post user_session_path, params: {
          user: { email: '<EMAIL>', password: 'wrong' }
        }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.body).to include("Invalid Email or password")
      end
    end
  end

  describe "GET /users/sign_up" do
    it "shows the registration page" do
      get new_user_registration_path
      expect(response).to have_http_status(:success)
      expect(response.body).to include("Create your DataRefinery account")
    end
  end

  describe "POST /users" do
    context "with valid params" do
      it "creates a new organization and user" do
        expect {
          post user_registration_path, params: {
            user: {
              email: '<EMAIL>',
              password: 'password123',
              password_confirmation: 'password123',
              first_name: 'John',
              last_name: 'Doe'
            },
            organization: {
              name: 'New Company'
            }
          }
        }.to change(Organization, :count).by(1).and change(User, :count).by(1)
        
        expect(response).to redirect_to(root_path)
        
        new_org = Organization.last
        expect(new_org.name).to eq('New Company')
        
        new_user = User.last
        expect(new_user.email).to eq('<EMAIL>')
        expect(new_user.organization).to eq(new_org)
        expect(new_user.role).to eq('admin')
      end
    end

    context "with invalid organization params" do
      it "shows error messages" do
        post user_registration_path, params: {
          user: {
            email: '<EMAIL>',
            password: 'password123',
            password_confirmation: 'password123'
          },
          organization: {
            name: ''
          }
        }
        
        expect(response).to have_http_status(:ok)
        expect(response.body).to include("Organization errors")
      end
    end
  end

  describe "DELETE /users/sign_out" do
    let(:organization) { Organization.create!(name: 'Test Org') }
    let(:user) { User.create!(email: '<EMAIL>', password: 'password123', organization: organization) }
    
    before { sign_in user }
    
    it "signs out the user" do
      delete destroy_user_session_path
      expect(response).to redirect_to(root_path)
      follow_redirect!
      expect(response.body).to include("Data integration")
      expect(response.body).not_to include("Welcome to DataRefinery")
    end
  end
end