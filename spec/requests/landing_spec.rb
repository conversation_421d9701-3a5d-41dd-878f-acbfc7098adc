require 'rails_helper'

RSpec.describe "Landings", type: :request do
  describe "GET /" do
    it "returns http success" do
      get root_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /about" do
    it "returns http success" do
      get about_path
      expect(response).to have_http_status(:success)
    end

    it "renders the about page with correct title" do
      get about_path
      expect(response.body).to include("About Data Reflow - Enterprise Analytics Platform")
    end

    it "includes company story content" do
      get about_path
      expect(response.body).to include("Our Story")
      expect(response.body).to include("Founded in 2023")
    end

    it "includes core values section" do
      get about_path
      expect(response.body).to include("Our Core Values")
      expect(response.body).to include("Accessibility")
      expect(response.body).to include("Innovation")
    end
  end

  describe "GET /contact" do
    it "returns http success" do
      get contact_path
      expect(response).to have_http_status(:success)
    end

    it "renders the contact page with correct title" do
      get contact_path
      expect(response.body).to include("Contact Data Reflow - Get in Touch")
    end

    it "includes contact options" do
      get contact_path
      expect(response.body).to include("How Can We Help You?")
      expect(response.body).to include("Schedule a Demo")
      expect(response.body).to include("Sales Inquiry")
      expect(response.body).to include("Technical Support")
    end

    it "includes contact form" do
      get contact_path
      expect(response.body).to include("Send Us a Message")
      expect(response.body).to include('name="first_name"')
      expect(response.body).to include('name="email"')
      expect(response.body).to include('name="message"')
    end

    it "includes FAQ section" do
      get contact_path
      expect(response.body).to include("Frequently Asked Questions")
      expect(response.body).to include("How quickly can I get started")
    end

    it "includes FAQ accordion functionality" do
      get contact_path
      expect(response.body).to include('data-controller="faq"')
      expect(response.body).to include('data-action="click->faq#toggle')
      expect(response.body).to include('data-faq-target="trigger"')
      expect(response.body).to include('data-faq-target="content"')
      expect(response.body).to include('data-faq-target="icon"')
    end

    it "includes proper ARIA attributes for accessibility" do
      get contact_path
      expect(response.body).to include('aria-expanded="false"')
      expect(response.body).to include('type="button"')
    end
  end

end
