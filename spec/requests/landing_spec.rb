require 'rails_helper'

RSpec.describe "Landings", type: :request do
  describe "GET /" do
    it "returns http success" do
      get root_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /about" do
    it "returns http success" do
      get about_path
      expect(response).to have_http_status(:success)
    end

    it "renders the about page with correct title" do
      get about_path
      expect(response.body).to include("About Data Reflow - Enterprise Analytics Platform")
    end

    it "includes company story content" do
      get about_path
      expect(response.body).to include("Our Story")
      expect(response.body).to include("Founded in 2023")
    end

    it "includes core values section" do
      get about_path
      expect(response.body).to include("Our Core Values")
      expect(response.body).to include("Accessibility")
      expect(response.body).to include("Innovation")
    end
  end

end
