// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application"

import AnimateController from "./animate_controller"
application.register("animate", AnimateController)

import Counter<PERSON><PERSON>roller from "./counter_controller"
application.register("counter", CounterController)

import CsvFormController from "./csv_form_controller"
application.register("csv-form", CsvFormController)

import DashboardNavigationController from "./dashboard_navigation_controller"
application.register("dashboard-navigation", DashboardNavigationController)

import DataSourceFormController from "./data_source_form_controller"
application.register("data-source-form", DataSourceFormController)

import ExpandableController from "./expandable_controller"
application.register("expandable", ExpandableController)

import FaqController from "./faq_controller"
application.register("faq", FaqController)

import FlashMessageController from "./flash_message_controller"
application.register("flash-message", FlashMessageController)

import Hello<PERSON><PERSON>roller from "./hello_controller"
application.register("hello", HelloController)

import LandingController from "./landing_controller"
application.register("landing", LandingController)

import LandingPageController from "./landing_page_controller"
application.register("landing-page", LandingPageController)

import ModalController from "./modal_controller"
application.register("modal", ModalController)

import NavigationController from "./navigation_controller"
application.register("navigation", NavigationController)

import RoiCalculatorController from "./roi_calculator_controller"
application.register("roi-calculator", RoiCalculatorController)

import ToggleController from "./toggle_controller"
application.register("toggle", ToggleController)
