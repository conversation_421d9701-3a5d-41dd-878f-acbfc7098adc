@tailwind base;
@tailwind components;
@tailwind utilities;

/* Accessibility utilities */
@layer utilities {
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

/* Base Styles */
@layer base {
  /* CSS Variables for dynamic theming */
  :root {
    /* Using Tailwind's color values */
    --color-primary: theme('colors.primary.DEFAULT');
    --color-primary-hover: theme('colors.primary.600');
    --color-primary-active: theme('colors.primary.700');
    
    /* Spacing variables */
    --space-1: theme('spacing.1');
    --space-2: theme('spacing.2');
    --space-4: theme('spacing.4');
    --space-8: theme('spacing.8');
    
    /* Border radius */
    --radius-sm: theme('borderRadius.sm');
    --radius-base: theme('borderRadius.DEFAULT');
    --radius-lg: theme('borderRadius.lg');
    
    /* Transitions */
    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  /* Focus styles for accessibility */
  *:focus {
    outline: none;
  }
  
  *:focus-visible {
    outline: 2px solid theme('colors.primary.DEFAULT');
    outline-offset: 2px;
    border-radius: theme('borderRadius.sm');
  }
  
  /* Skip link for accessibility */
  .skip-link {
    @apply absolute left-0 top-0 bg-primary-500 text-white px-4 py-2 z-50;
    transform: translateY(-100%);
  }
  
  .skip-link:focus {
    transform: translateY(0%);
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Selection colors */
  ::selection {
    @apply bg-primary-500 text-white;
  }
  
  /* Form elements default styles */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  textarea,
  select {
    @apply w-full px-4 py-2 border border-neutral-300 rounded-lg;
    @apply focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20;
    @apply transition-colors duration-200;
  }
  
  /* Links */
  a {
    @apply text-primary-500 hover:text-primary-700 transition-colors;
  }
}

/* Component Styles */
@layer components {
  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 font-medium rounded-lg;
    @apply transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn--primary {
    @apply bg-primary-500 text-white;
    @apply hover:bg-primary-600 active:bg-primary-700;
    @apply focus:ring-primary-500;
  }
  
  .btn--secondary {
    @apply bg-white text-neutral-700 border border-neutral-300;
    @apply hover:bg-neutral-100 active:bg-neutral-200;
    @apply focus:ring-neutral-500;
  }
  
  .btn--danger {
    @apply bg-red-600 text-white;
    @apply hover:bg-red-700 active:bg-red-800;
    @apply focus:ring-red-600;
  }
  
  .btn--large {
    @apply px-6 py-3 text-lg;
  }
  
  .btn--small {
    @apply px-3 py-1.5 text-sm;
  }
  
  /* Cards */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-neutral-200;
    @apply transition-shadow duration-200;
  }
  
  .card:hover {
    @apply shadow-md;
  }
  
  .card__header {
    @apply px-6 py-4 border-b border-neutral-200;
  }
  
  .card__body {
    @apply p-6;
  }
  
  .card__footer {
    @apply px-6 py-4 border-t border-neutral-200 bg-neutral-100;
  }
  
  /* Status Indicators */
  .status {
    @apply inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium;
  }
  
  .status--success {
    @apply bg-primary-50 text-primary-600;
  }
  
  .status--error {
    @apply bg-red-50 text-red-600;
  }
  
  .status--warning {
    @apply bg-yellow-50 text-yellow-600;
  }
  
  .status--info {
    @apply bg-blue-50 text-blue-600;
  }
  
  /* Form Groups */
  .form-group {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium text-neutral-700 mb-1;
  }
  
  .form-hint {
    @apply text-sm text-neutral-600 mt-1;
  }
  
  .form-error {
    @apply text-sm text-red-600 mt-1;
  }
  
  /* Alerts */
  .alert {
    @apply p-4 rounded-lg border;
  }
  
  .alert--success {
    @apply bg-primary-50 border-primary-600 text-primary-600;
  }
  
  .alert--error {
    @apply bg-red-50 border-red-600 text-red-600;
  }
  
  .alert--warning {
    @apply bg-yellow-50 border-yellow-600 text-yellow-600;
  }
  
  .alert--info {
    @apply bg-blue-50 border-blue-600 text-blue-600;
  }
  
  /* Navigation */
  .nav-link {
    @apply text-neutral-600 hover:text-neutral-900 px-3 py-2 rounded-md text-sm font-medium;
    @apply transition-colors duration-200;
  }
  
  .nav-link.active {
    @apply text-primary-600 bg-primary-50;
  }
  
  /* Tables */
  .table {
    @apply w-full divide-y divide-neutral-200;
  }
  
  .table thead {
    @apply bg-neutral-100;
  }
  
  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider;
  }
  
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-neutral-900;
  }
  
  .table tbody tr:hover {
    @apply bg-neutral-100;
  }
  
  /* Badges */
  .badge {
    @apply inline-flex items-center px-2 py-0.5 rounded text-xs font-medium;
  }
  
  .badge--primary {
    @apply bg-primary-100 text-primary-800;
  }
  
  .badge--neutral {
    @apply bg-neutral-100 text-neutral-800;
  }
  
  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-neutral-200 rounded;
  }
  
  .spinner {
    @apply animate-spin h-5 w-5 text-primary-500;
  }
}

/* Utility Overrides */
@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Custom animations using Tailwind's animation utilities */
  .animate-fade-in {
    animation: fadeIn 250ms var(--ease-standard);
  }
  
  .animate-slide-up {
    animation: slideUp 250ms var(--ease-standard);
  }
  
  .animate-scale-in {
    animation: scaleIn 250ms var(--ease-standard);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply bg-white text-black;
  }
  
  a {
    @apply text-black underline;
  }
}

/* Landing Page Styles */
@layer components {
  /* Header & Navigation */
  .header {
    @apply fixed top-0 w-full bg-white shadow-sm z-50;
  }
  
  .nav {
    @apply flex items-center justify-between h-16 px-4 md:px-8;
  }
  
  .nav__brand {
    @apply flex items-center;
  }
  
  .nav__logo {
    @apply text-2xl font-bold text-primary-600;
  }
  
  .nav__toggle {
    @apply md:hidden flex flex-col justify-center items-center w-8 h-8;
  }
  
  .nav__toggle-bar {
    @apply w-6 h-0.5 bg-neutral-700 mb-1 last:mb-0 transition-all;
  }
  
  .nav__menu {
    @apply hidden md:flex items-center space-x-8 list-none;
  }
  
  .nav__link {
    @apply text-neutral-700 hover:text-primary-600 font-medium transition-colors;
  }
  
  .nav__actions {
    @apply hidden md:flex items-center space-x-4;
  }
  
  /* Hero Section */
  .hero {
    @apply pt-24 pb-16 bg-gradient-to-br from-neutral-bg to-neutral-100;
  }
  
  .hero__content {
    @apply grid md:grid-cols-2 gap-12 items-center;
  }
  
  .hero__title {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 mb-6;
  }
  
  .hero__highlight {
    @apply text-primary-600;
  }
  
  .hero__subtitle {
    @apply text-xl text-neutral-600 mb-8;
  }
  
  .hero__actions {
    @apply flex flex-wrap gap-4 mb-12;
  }
  
  .hero__metrics {
    @apply grid grid-cols-3 gap-8;
  }
  
  .hero__image {
    @apply relative;
  }
  
  .hero__img {
    @apply w-full h-auto rounded-lg shadow-xl;
  }
  
  /* Metrics */
  .metric {
    @apply text-center;
  }
  
  .metric__number {
    @apply text-3xl font-bold text-primary-600;
  }
  
  .metric__label {
    @apply text-sm text-neutral-600;
  }
  
  /* Sections */
  .section__header {
    @apply text-center mb-12;
  }
  
  .section__title {
    @apply text-3xl md:text-4xl font-bold text-neutral-900 mb-4;
  }
  
  .section__subtitle {
    @apply text-lg text-neutral-600 max-w-2xl mx-auto;
  }
  
  /* Testimonials */
  .testimonials {
    @apply py-16 bg-neutral-100;
  }
  
  .testimonials__grid {
    @apply grid md:grid-cols-2 lg:grid-cols-3 gap-8;
  }
  
  .testimonial {
    @apply bg-white rounded-lg p-8;
  }
  
  .testimonial__quote {
    @apply text-neutral-700 italic mb-4;
  }
  
  .testimonial__metrics {
    @apply flex flex-wrap gap-2 mb-6;
  }
  
  .testimonial__author {
    @apply flex items-center;
  }
  
  .testimonial__avatar {
    @apply w-12 h-12 rounded-full mr-4;
  }
  
  .testimonial__name {
    @apply font-semibold text-neutral-900;
  }
  
  .testimonial__title {
    @apply text-sm text-neutral-600;
  }
  
  .testimonial__company {
    @apply text-sm text-primary-600;
  }
  
  /* ROI Section */
  .roi-section {
    @apply py-16 bg-primary-900 text-white;
  }
  
  .roi__content {
    @apply grid md:grid-cols-2 gap-12 items-center;
  }
  
  .roi__title {
    @apply text-3xl md:text-4xl font-bold mb-4;
  }
  
  .roi__description {
    @apply text-lg mb-8 opacity-90;
  }
  
  .roi__stats {
    @apply grid grid-cols-2 md:grid-cols-3 gap-6;
  }
  
  .roi__stat {
    @apply text-center;
  }
  
  .roi__number {
    @apply text-3xl font-bold;
  }
  
  .roi__label {
    @apply text-sm opacity-80;
  }
  
  .roi__img {
    @apply w-full h-auto rounded-lg;
  }
  
  /* Dashboard Showcase */
  .dashboard-showcase {
    @apply py-16 bg-white;
  }
  
  .dashboard__grid {
    @apply grid md:grid-cols-3 gap-8;
  }
  
  .dashboard__item {
    @apply bg-white rounded-lg shadow-lg overflow-hidden;
  }
  
  .dashboard__image {
    @apply w-full h-48 object-cover;
  }
  
  .dashboard__content {
    @apply p-6;
  }
  
  .dashboard__title {
    @apply text-xl font-semibold text-neutral-900 mb-2;
  }
  
  .dashboard__description {
    @apply text-neutral-600;
  }
  
  /* Features */
  .features {
    @apply py-16 bg-neutral-100;
  }
  
  .features__grid {
    @apply grid md:grid-cols-2 gap-8;
  }
  
  .feature {
    @apply bg-white rounded-lg overflow-hidden shadow-sm;
  }
  
  .feature__image {
    @apply h-48 overflow-hidden;
  }
  
  .feature__img {
    @apply w-full h-full object-cover;
  }
  
  .feature__content {
    @apply p-6;
  }
  
  .feature__title {
    @apply text-xl font-semibold text-neutral-900 mb-2;
  }
  
  .feature__description {
    @apply text-neutral-600 mb-4;
  }
  
  .feature__benefit {
    @apply inline-block;
  }
  
  /* Happy Customers */
  .happy-customers {
    @apply py-16 bg-primary-50;
  }
  
  .happy-customers__content {
    @apply text-center;
  }
  
  .happy-customers__title {
    @apply text-3xl font-bold text-neutral-900 mb-4;
  }
  
  .happy-customers__description {
    @apply text-lg text-neutral-600 mb-8;
  }
  
  .happy-customers__images {
    @apply flex justify-center gap-4;
  }
  
  .happy-customers__img {
    @apply w-48 h-48 rounded-lg object-cover;
  }
  
  /* Pricing */
  .pricing {
    @apply py-16 bg-white;
  }
  
  .pricing__grid {
    @apply grid md:grid-cols-3 gap-8 max-w-5xl mx-auto;
  }
  
  .pricing__card {
    @apply relative bg-white rounded-lg shadow-lg p-8 border-2 border-transparent;
  }
  
  .pricing__card--popular {
    @apply border-primary-500 transform scale-105;
  }
  
  .pricing__badge {
    @apply absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium;
  }
  
  .pricing__plan {
    @apply text-2xl font-bold text-neutral-900 mb-2;
  }
  
  .pricing__price {
    @apply flex items-baseline justify-center mb-2;
  }
  
  .pricing__currency {
    @apply text-lg text-neutral-600;
  }
  
  .pricing__amount {
    @apply text-5xl font-bold text-neutral-900 mx-1;
  }
  
  .pricing__period {
    @apply text-lg text-neutral-600;
  }
  
  .pricing__target {
    @apply text-sm text-neutral-600 mb-6;
  }
  
  .pricing__features {
    @apply space-y-3 mb-8 list-none;
  }
  
  .pricing__features li {
    @apply flex items-start;
  }
  
  .pricing__features li::before {
    content: "✓";
    @apply text-green-500 font-bold mr-2;
  }
  
  /* CTA Section */
  .cta {
    @apply py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white;
  }
  
  .cta__content {
    @apply text-center;
  }
  
  .cta__title {
    @apply text-3xl md:text-4xl font-bold mb-4;
  }
  
  .cta__description {
    @apply text-lg mb-8 opacity-90;
  }
  
  .cta__actions {
    @apply flex flex-wrap gap-4 justify-center;
  }
  
  /* Footer */
  .footer {
    @apply py-12 bg-neutral-900 text-white;
  }
  
  .footer__content {
    @apply grid md:grid-cols-4 gap-8 mb-8;
  }
  
  .footer__logo {
    @apply text-2xl font-bold mb-2;
  }
  
  .footer__description {
    @apply text-neutral-400;
  }
  
  .footer__title {
    @apply font-semibold mb-4;
  }
  
  .footer__list {
    @apply space-y-2 list-none;
  }
  
  .footer__link {
    @apply text-neutral-400 hover:text-white transition-colors;
  }
  
  .footer__bottom {
    @apply pt-8 border-t border-neutral-800 text-center text-neutral-400;
  }
  
  /* Button variants */
  .btn--outline {
    @apply bg-transparent border-2 border-current;
  }
  
  .btn--lg {
    @apply px-8 py-4 text-lg;
  }
  
  .btn--full-width {
    @apply w-full;
  }

  /* Flash Messages */
  .flash-message-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .flash-message-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.3s ease-out;
  }

  .flash-message-exit {
    opacity: 1;
    transform: translateX(0);
  }

  .flash-message-exit-active {
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease-in;
  }

  /* Progress bar animation */
  .flash-progress {
    transition: transform 0.1s ease-out;
  }

  .flash-progress-active {
    animation: flash-progress-fill linear forwards;
  }

  @keyframes flash-progress-fill {
    from {
      transform: scaleX(0);
    }
    to {
      transform: scaleX(1);
    }
  }

  /* Hover effects for flash messages */
  .flash-message:hover {
    transform: scale(1.01);
    transition: transform 0.2s ease-out;
  }

  /* Focus styles for accessibility */
  .flash-message:focus {
    outline: 2px solid #21808D;
    outline-offset: 2px;
  }
}