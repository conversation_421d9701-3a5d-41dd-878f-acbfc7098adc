require 'fileutils'

class Dashboard::DataSourcesController < Dashboard::BaseController
  before_action :set_data_source, only: [:show, :edit, :update, :destroy, :test_connection, :sync, :schema, :sample_data]
  
  def index
    @data_sources = current_organization.data_sources.order(created_at: :desc)
    @stats = {
      total: @data_sources.count,
      active: @data_sources.active.count,
      with_errors: @data_sources.with_errors.count,
      syncing: @data_sources.where(status: 'syncing').count
    }
  end
  
  def test
    # Test page for debugging Stimulus controller
  end
  
  def show
    @recent_syncs = [] # TODO: Add sync history
  end
  
  def new
    @data_source = current_organization.data_sources.build
    @source_types = DataSource::SOURCE_TYPES.map do |type|
      [DataSource.new(source_type: type).source_type_text, type]
    end.sort_by(&:first)
  end
  
  def create
    @data_source = current_organization.data_sources.build(data_source_params)
    
    # Handle CSV file upload
    if @data_source.source_type == 'csv' && params[:data_source][:csv_file].present?
      handle_csv_file_upload(@data_source, params[:data_source][:csv_file])
    end
    
    if @data_source.save
      # Test connection if requested
      if params[:test_connection] == 'true'
        result = @data_source.test_connection
        if result[:success]
          flash[:notice] = "Data source created and connection successful!"
        else
          flash[:alert] = "Data source created but connection failed: #{result[:error]}"
        end
      else
        flash[:notice] = "Data source created successfully!"
      end
      
      redirect_to dashboard_data_source_path(@data_source)
    else
      @source_types = DataSource::SOURCE_TYPES.map do |type|
        [DataSource.new(source_type: type).source_type_text, type]
      end.sort_by(&:first)
      render :new, status: :unprocessable_entity
    end
  end
  
  def edit
    @source_types = DataSource::SOURCE_TYPES.map do |type|
      [DataSource.new(source_type: type).source_type_text, type]
    end.sort_by(&:first)
  end
  
  def update
    # Handle CSV file upload for updates
    if @data_source.source_type == 'csv' && params[:data_source][:csv_file].present?
      handle_csv_file_upload(@data_source, params[:data_source][:csv_file])
    end
    
    if @data_source.update(data_source_params)
      flash[:notice] = "Data source updated successfully!"
      redirect_to dashboard_data_source_path(@data_source)
    else
      @source_types = DataSource::SOURCE_TYPES.map do |type|
        [DataSource.new(source_type: type).source_type_text, type]
      end.sort_by(&:first)
      render :edit, status: :unprocessable_entity
    end
  end
  
  def destroy
    @data_source.destroy
    flash[:notice] = "Data source deleted successfully!"
    redirect_to dashboard_data_sources_path
  end
  
  # Connection fields partial loader
  def connection_fields
    source_type = params[:source_type]
    
    if source_type.present? && DataSource::SOURCE_TYPES.include?(source_type)
      @data_source = current_organization.data_sources.build(source_type: source_type)
      
      begin
        render partial: "dashboard/data_sources/connection_fields/#{source_type}", 
               locals: { data_source: @data_source, form: nil }
      rescue ActionView::MissingTemplate
        render plain: "<div class='bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg'>
                        <p class='font-medium'>Coming Soon</p>
                        <p class='text-sm mt-1'>Connection settings for #{source_type.humanize} are not yet implemented.</p>
                      </div>", 
               status: :ok
      end
    else
      render plain: "Invalid source type", status: :bad_request
    end
  end
  
  # Custom actions
  def test_connection
    result = @data_source.test_connection
    
    if result[:success]
      flash[:notice] = "Connection successful!"
    else
      flash[:alert] = "Connection failed: #{result[:error]}"
    end
    
    redirect_to dashboard_data_source_path(@data_source)
  end
  
  def sync
    result = @data_source.sync!
    
    if result[:success]
      flash[:notice] = result[:message]
    else
      flash[:alert] = "Sync failed: #{result[:error]}"
    end
    
    redirect_to dashboard_data_source_path(@data_source)
  end
  
  def schema
    @schema_info = @data_source.fetch_schema
    
    if @schema_info[:success]
      @schema = @schema_info[:schema]
    else
      flash[:alert] = "Failed to fetch schema: #{@schema_info[:error]}"
      redirect_to dashboard_data_source_path(@data_source)
    end
  end
  
  def sample_data
    @sample_data_result = @data_source.fetch_sample_data(limit: 20)
    
    if @sample_data_result[:success]
      @sample_data = @sample_data_result[:data]
      @columns = @sample_data_result[:metadata][:columns]
    else
      flash[:alert] = "Failed to fetch sample data: #{@sample_data_result[:error]}"
      redirect_to dashboard_data_source_path(@data_source)
    end
  end
  
  private
  
  def set_data_source
    @data_source = current_organization.data_sources.find(params[:id])
  end
  
  def data_source_params
    params.require(:data_source).permit(
      :name, 
      :description,
      :source_type, 
      :sync_frequency,
      :active,
      connection_config: [
        :file_path, :file_url, :delimiter, :encoding, :has_headers,
        :username, :password, :s3_config, :transformations,
        :host, :port, :database, :sslmode
      ]
    ).tap do |p|
      # Handle nested connection_config parameters
      if p[:connection_config].present?
        # Parse JSON fields if they're strings
        [:s3_config, :transformations].each do |field|
          if p[:connection_config][field].is_a?(String) && p[:connection_config][field].present?
            begin
              p[:connection_config][field] = JSON.parse(p[:connection_config][field])
            rescue JSON::ParserError
              # Keep as string if not valid JSON
            end
          end
        end
        
        # Convert connection_config hash to JSON string for storage
        p[:connection_config] = p[:connection_config].to_json
      end
    end
  end
  
  def current_organization
    current_user.organization
  end
  
  def handle_csv_file_upload(data_source, uploaded_file)
    # Create uploads directory if it doesn't exist
    uploads_dir = Rails.root.join('storage', 'uploads', 'csv', current_organization.id.to_s)
    FileUtils.mkdir_p(uploads_dir)
    
    # Generate unique filename
    timestamp = Time.current.strftime('%Y%m%d%H%M%S')
    filename = "#{timestamp}_#{uploaded_file.original_filename}"
    file_path = uploads_dir.join(filename)
    
    # Save the file
    File.open(file_path, 'wb') do |file|
      file.write(uploaded_file.read)
    end
    
    # Update connection config with file path
    config = data_source.connection_config.present? ? JSON.parse(data_source.connection_config) : {}
    config['file_path'] = file_path.to_s
    config['original_filename'] = uploaded_file.original_filename
    config['file_size'] = uploaded_file.size
    config['uploaded_at'] = Time.current
    
    data_source.connection_config = config.to_json
  rescue => e
    Rails.logger.error "Failed to handle CSV upload: #{e.message}"
    data_source.errors.add(:base, "Failed to upload CSV file: #{e.message}")
  end
end