class ErrorsController < ApplicationController
  skip_before_action :authenticate_user!
  
  def not_found
    respond_to do |format|
      format.html { render status: 404 }
      format.json { render json: { error: "Not found" }, status: 404 }
    end
  end
  
  def unprocessable
    respond_to do |format|
      format.html { render status: 422 }
      format.json { render json: { error: "Unprocessable entity" }, status: 422 }
    end
  end
  
  def internal_server
    respond_to do |format|
      format.html { render status: 500 }
      format.json { render json: { error: "Internal server error" }, status: 500 }
    end
  end
end