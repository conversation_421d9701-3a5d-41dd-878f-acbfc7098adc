<% content_for :title, "Contact Data Reflow - Get in Touch" %>
<% content_for :description, "Contact Data Reflow for demos, support, or partnership opportunities. Our team is ready to help you transform your data strategy." %>
<% content_for :body_class, "contact-page" %>

<!-- Font Awesome Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

<div data-controller="landing-page">
<!-- Hero Section -->
<section class="pt-24 lg:pt-32 pb-16 lg:pb-24 bg-gradient-to-br from-slate-900 via-slate-800 to-primary-900 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
  </div>
  
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative">
    <div class="max-w-4xl mx-auto text-center text-white">
      <!-- Badge -->
      <div class="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-8">
        <i class="fas fa-headset text-primary-400 text-sm"></i>
        <span class="text-sm font-medium">24/7 Support Available</span>
      </div>

      <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
        Get in Touch with
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-primary-400">
          Our Team
        </span>
      </h1>

      <p class="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
        Ready to transform your data strategy? Our experts are here to help you get started 
        with Data Reflow and unlock your business potential.
      </p>
    </div>
  </div>
</section>

<!-- Contact Options Section -->
<section class="py-16 lg:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        How Can We Help You?
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Choose the best way to connect with our team based on your needs.
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Schedule Demo -->
      <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl p-8 text-center hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="100">
        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-calendar-alt text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Schedule a Demo</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          See Data Reflow in action with a personalized demo tailored to your industry and use cases.
        </p>
        <a href="#demo"
           class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold inline-flex items-center space-x-2"
           data-action="click->landing#openDemoModal">
          <i class="fas fa-play text-sm"></i>
          <span>Book Demo</span>
        </a>
      </div>

      <!-- Sales Inquiry -->
      <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8 text-center hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-handshake text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Sales Inquiry</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          Discuss pricing, enterprise features, and custom solutions with our sales team.
        </p>
        <a href="mailto:<EMAIL>"
           class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all duration-200 font-semibold inline-flex items-center space-x-2">
          <i class="fas fa-envelope text-sm"></i>
          <span>Contact Sales</span>
        </a>
      </div>

      <!-- Technical Support -->
      <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8 text-center hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="300">
        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-life-ring text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Technical Support</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          Get help with implementation, troubleshooting, or technical questions from our experts.
        </p>
        <a href="mailto:<EMAIL>"
           class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-all duration-200 font-semibold inline-flex items-center space-x-2">
          <i class="fas fa-tools text-sm"></i>
          <span>Get Support</span>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Contact Form Section -->
<section class="py-16 lg:py-24 bg-neutral-bg">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-12" data-controller="animate" data-animate-animation-value="fadeInUp">
        <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
          Send Us a Message
        </h2>
        <p class="text-xl text-neutral-600 leading-relaxed">
          Have a specific question or want to discuss your data challenges? Fill out the form below and we'll get back to you within 24 hours.
        </p>
      </div>

      <div class="bg-white rounded-xl shadow-lg p-8 lg:p-12" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
        <form class="space-y-6" data-controller="contact-form" data-action="submit->contact-form#submit">
          <div class="grid md:grid-cols-2 gap-6">
            <!-- First Name -->
            <div>
              <label for="first_name" class="block text-sm font-medium text-neutral-700 mb-2">
                First Name *
              </label>
              <input type="text" 
                     id="first_name" 
                     name="first_name" 
                     required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                     placeholder="Enter your first name">
            </div>

            <!-- Last Name -->
            <div>
              <label for="last_name" class="block text-sm font-medium text-neutral-700 mb-2">
                Last Name *
              </label>
              <input type="text" 
                     id="last_name" 
                     name="last_name" 
                     required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                     placeholder="Enter your last name">
            </div>
          </div>

          <div class="grid md:grid-cols-2 gap-6">
            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-neutral-700 mb-2">
                Email Address *
              </label>
              <input type="email" 
                     id="email" 
                     name="email" 
                     required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                     placeholder="Enter your email address">
            </div>

            <!-- Company -->
            <div>
              <label for="company" class="block text-sm font-medium text-neutral-700 mb-2">
                Company
              </label>
              <input type="text" 
                     id="company" 
                     name="company"
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                     placeholder="Enter your company name">
            </div>
          </div>

          <!-- Subject -->
          <div>
            <label for="subject" class="block text-sm font-medium text-neutral-700 mb-2">
              Subject *
            </label>
            <select id="subject" 
                    name="subject" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
              <option value="">Select a topic</option>
              <option value="demo">Request a Demo</option>
              <option value="sales">Sales Inquiry</option>
              <option value="support">Technical Support</option>
              <option value="partnership">Partnership Opportunity</option>
              <option value="general">General Question</option>
            </select>
          </div>

          <!-- Message -->
          <div>
            <label for="message" class="block text-sm font-medium text-neutral-700 mb-2">
              Message *
            </label>
            <textarea id="message" 
                      name="message" 
                      rows="6" 
                      required
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors resize-vertical"
                      placeholder="Tell us about your data challenges and how we can help..."></textarea>
          </div>

          <!-- Submit Button -->
          <div class="text-center">
            <button type="submit"
                    class="bg-primary-600 text-white px-8 py-4 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 inline-flex items-center space-x-2">
              <i class="fas fa-paper-plane text-sm"></i>
              <span>Send Message</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<!-- Contact Information Section -->
<section class="py-16 lg:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
        <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
          Other Ways to Reach Us
        </h2>
        <p class="text-xl text-neutral-600 leading-relaxed">
          Multiple channels to connect with our team based on your preference.
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Email -->
        <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="100">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-envelope text-white text-2xl"></i>
          </div>
          <h3 class="text-lg font-bold text-neutral-800 mb-2">Email</h3>
          <p class="text-neutral-600 mb-4">General inquiries</p>
          <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700 font-medium">
            <EMAIL>
          </a>
        </div>

        <!-- Phone -->
        <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-phone text-white text-2xl"></i>
          </div>
          <h3 class="text-lg font-bold text-neutral-800 mb-2">Phone</h3>
          <p class="text-neutral-600 mb-4">Mon-Fri, 9AM-6PM EST</p>
          <a href="tel:******-DATA-FLOW" class="text-primary-600 hover:text-primary-700 font-medium">
            +1 (555) DATA-FLOW
          </a>
        </div>

        <!-- Live Chat -->
        <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="300">
          <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-comments text-white text-2xl"></i>
          </div>
          <h3 class="text-lg font-bold text-neutral-800 mb-2">Live Chat</h3>
          <p class="text-neutral-600 mb-4">24/7 instant support</p>
          <button class="text-primary-600 hover:text-primary-700 font-medium"
                  data-action="click->landing#openChat">
            Start Chat
          </button>
        </div>

        <!-- Help Center -->
        <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="400">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-question-circle text-white text-2xl"></i>
          </div>
          <h3 class="text-lg font-bold text-neutral-800 mb-2">Help Center</h3>
          <p class="text-neutral-600 mb-4">Self-service resources</p>
          <a href="#" class="text-primary-600 hover:text-primary-700 font-medium">
            Browse Articles
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-16 lg:py-24 bg-neutral-bg">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
        <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
          Frequently Asked Questions
        </h2>
        <p class="text-xl text-neutral-600 leading-relaxed">
          Quick answers to common questions about Data Reflow.
        </p>
      </div>

      <div class="space-y-6" data-controller="faq">
        <!-- FAQ Item 1 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="100">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle"
                  data-faq-target="trigger">
            <span class="text-lg font-semibold text-neutral-800">How quickly can I get started with Data Reflow?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4 hidden" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              Most customers are up and running within 24-48 hours. Our onboarding team will help you connect your data sources,
              configure your first dashboards, and train your team. Enterprise customers typically see full deployment within 1-2 weeks.
            </p>
          </div>
        </div>

        <!-- FAQ Item 2 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle"
                  data-faq-target="trigger">
            <span class="text-lg font-semibold text-neutral-800">What data sources does Data Reflow support?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4 hidden" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              We support 200+ data connectors including databases (MySQL, PostgreSQL, MongoDB), cloud platforms (AWS, Azure, GCP),
              business applications (Salesforce, HubSpot, Shopify), and file formats (CSV, JSON, Excel). Custom connectors available for enterprise customers.
            </p>
          </div>
        </div>

        <!-- FAQ Item 3 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="300">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle"
                  data-faq-target="trigger">
            <span class="text-lg font-semibold text-neutral-800">Is my data secure with Data Reflow?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4 hidden" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              Absolutely. We use enterprise-grade security including AES-256 encryption, SOC 2 Type II compliance, GDPR compliance,
              and role-based access controls. Your data is processed in secure, isolated environments and never shared with third parties.
            </p>
          </div>
        </div>

        <!-- FAQ Item 4 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="400">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle"
                  data-faq-target="trigger">
            <span class="text-lg font-semibold text-neutral-800">Do you offer training and support?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4 hidden" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              Yes! We provide comprehensive onboarding, live training sessions, extensive documentation, video tutorials,
              and 24/7 support. Enterprise customers get dedicated success managers and priority support.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="py-16 lg:py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-primary-900 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
  </div>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
    <div data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
        Ready to Get Started?
      </h2>
      <p class="text-xl text-primary-100 mb-8 max-w-3xl mx-auto leading-relaxed">
        Don't let data complexity hold your business back. Contact us today and discover how
        Data Reflow can transform your data into your competitive advantage.
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="#demo"
           class="bg-primary-600 text-white px-8 py-4 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 inline-flex items-center justify-center space-x-2"
           data-action="click->landing#openDemoModal">
          <i class="fas fa-rocket text-sm"></i>
          <span>Start Free Trial</span>
        </a>
        <a href="mailto:<EMAIL>"
           class="border-2 border-white text-white px-8 py-4 rounded-lg hover:bg-white hover:text-neutral-800 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2">
          <i class="fas fa-envelope text-sm"></i>
          <span>Email Us</span>
        </a>
      </div>
    </div>
  </div>
</section>
</div>
