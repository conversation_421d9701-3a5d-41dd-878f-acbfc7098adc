<% content_for :title, "#{@data_source.name} - Data Sources" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="flex-1 min-w-0">
      <nav class="flex items-center text-sm mb-2">
        <%= link_to "Data Sources", dashboard_data_sources_path, class: "text-neutral-500 hover:text-neutral-700" %>
        <span class="mx-2 text-neutral-400">/</span>
        <span class="text-neutral-900"><%= @data_source.name %></span>
      </nav>
      <h2 class="text-2xl font-bold text-neutral-900 flex items-center">
        <%= @data_source.name %>
        <% case @data_source.status %>
        <% when 'active' %>
          <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
            <i class="fas fa-check-circle mr-1"></i> Active
          </span>
        <% when 'syncing' %>
          <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            <i class="fas fa-sync-alt animate-spin mr-1"></i> Syncing
          </span>
        <% when 'error' %>
          <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
            <i class="fas fa-exclamation-triangle mr-1"></i> Error
          </span>
        <% when 'inactive' %>
          <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
            <i class="fas fa-pause-circle mr-1"></i> Inactive
          </span>
        <% end %>
      </h2>
    </div>
    <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
      <%= link_to edit_dashboard_data_source_path(@data_source), class: "btn btn--secondary" do %>
        <i class="fas fa-edit mr-2"></i> Edit
      <% end %>
      <% if @data_source.can_sync? %>
        <%= button_to sync_dashboard_data_source_path(@data_source), method: :post, class: "btn btn--primary" do %>
          <i class="fas fa-sync-alt mr-2"></i> Sync Now
        <% end %>
      <% end %>
    </div>
  </div>

  <% if @data_source.error_message.present? %>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
      <p class="font-medium">Error: <%= @data_source.error_message %></p>
    </div>
  <% end %>

  <!-- Info Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Connection Info -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-neutral-900 mb-4">Connection Details</h3>
      <dl class="space-y-3">
        <div>
          <dt class="text-sm font-medium text-neutral-500">Type</dt>
          <dd class="text-sm text-neutral-900"><%= @data_source.source_type_text %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-neutral-500">Sync Frequency</dt>
          <dd class="text-sm text-neutral-900"><%= @data_source.sync_frequency_text %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-neutral-500">Created</dt>
          <dd class="text-sm text-neutral-900"><%= @data_source.created_at.strftime("%B %d, %Y") %></dd>
        </div>
      </dl>
      <div class="mt-4 pt-4 border-t">
        <%= button_to test_connection_dashboard_data_source_path(@data_source), method: :post, class: "btn btn--secondary btn--small w-full" do %>
          <i class="fas fa-plug mr-2"></i> Test Connection
        <% end %>
      </div>
    </div>

    <!-- Sync Status -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-neutral-900 mb-4">Sync Status</h3>
      <dl class="space-y-3">
        <div>
          <dt class="text-sm font-medium text-neutral-500">Last Sync</dt>
          <dd class="text-sm text-neutral-900">
            <% if @data_source.last_sync_at %>
              <%= time_ago_in_words(@data_source.last_sync_at) %> ago
              <span class="text-neutral-500">(<%= @data_source.last_sync_at.strftime("%b %d, %I:%M %p") %>)</span>
            <% else %>
              Never synced
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-neutral-500">Total Records</dt>
          <dd class="text-sm text-neutral-900"><%= @data_source.formatted_row_count %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-neutral-500">Next Sync</dt>
          <dd class="text-sm text-neutral-900">
            <% if @data_source.sync_frequency == 'manual' %>
              Manual only
            <% elsif @data_source.active? %>
              <%= @data_source.calculate_next_sync_time&.strftime("%b %d, %I:%M %p") || "Scheduled" %>
            <% else %>
              Not scheduled (inactive)
            <% end %>
          </dd>
        </div>
      </dl>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-neutral-900 mb-4">Quick Actions</h3>
      <div class="space-y-3">
        <%= link_to schema_dashboard_data_source_path(@data_source), class: "flex items-center text-primary-600 hover:text-primary-700" do %>
          <i class="fas fa-database mr-3"></i>
          <span>View Schema</span>
        <% end %>
        <%= link_to sample_data_dashboard_data_source_path(@data_source), class: "flex items-center text-primary-600 hover:text-primary-700" do %>
          <i class="fas fa-table mr-3"></i>
          <span>Preview Sample Data</span>
        <% end %>
        <a href="#" class="flex items-center text-primary-600 hover:text-primary-700">
          <i class="fas fa-history mr-3"></i>
          <span>View Sync History</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Tabs for additional info -->
  <div class="bg-white shadow rounded-lg">
    <div class="border-b border-neutral-200">
      <nav class="-mb-px flex">
        <a href="#" class="border-b-2 border-primary-500 py-4 px-6 text-sm font-medium text-primary-600">
          Overview
        </a>
        <a href="#" class="border-b-2 border-transparent py-4 px-6 text-sm font-medium text-neutral-500 hover:text-neutral-700 hover:border-neutral-300">
          Pipelines
        </a>
        <a href="#" class="border-b-2 border-transparent py-4 px-6 text-sm font-medium text-neutral-500 hover:text-neutral-700 hover:border-neutral-300">
          Settings
        </a>
      </nav>
    </div>
    
    <div class="p-6">
      <h3 class="text-lg font-medium text-neutral-900 mb-4">Recent Activity</h3>
      <p class="text-neutral-600">Activity logging will be displayed here once implemented.</p>
    </div>
  </div>
</div>