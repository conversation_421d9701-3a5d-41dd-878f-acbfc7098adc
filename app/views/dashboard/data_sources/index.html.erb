<% content_for :title, "Data Sources - Data Reflow" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-neutral-900 sm:text-3xl sm:truncate">
        Data Sources
      </h2>
      <p class="mt-1 text-sm text-neutral-600">
        Connect and manage your data sources
      </p>
    </div>
    <div class="mt-4 flex md:mt-0 md:ml-4">
      <%= link_to new_dashboard_data_source_path, class: "btn btn--primary" do %>
        <i class="fas fa-plus mr-2"></i>
        Add Data Source
      <% end %>
    </div>
  </div>

  <!-- Stats -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-4 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-database text-2xl text-neutral-400"></i>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-neutral-500 truncate">Total Sources</dt>
              <dd class="text-2xl font-semibold text-neutral-900"><%= @stats[:total] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-check-circle text-2xl text-green-500"></i>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-neutral-500 truncate">Active</dt>
              <dd class="text-2xl font-semibold text-neutral-900"><%= @stats[:active] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-sync-alt text-2xl text-blue-500 animate-spin"></i>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-neutral-500 truncate">Syncing</dt>
              <dd class="text-2xl font-semibold text-neutral-900"><%= @stats[:syncing] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-2xl text-red-500"></i>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-neutral-500 truncate">Errors</dt>
              <dd class="text-2xl font-semibold text-neutral-900"><%= @stats[:with_errors] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Sources List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @data_sources.any? %>
      <ul class="divide-y divide-neutral-200">
        <% @data_sources.each do |data_source| %>
          <li>
            <%= link_to dashboard_data_source_path(data_source), class: "block hover:bg-neutral-50" do %>
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <% icon_class = case data_source.source_type
                         when 'postgresql', 'mysql', 'sqlite', 'sqlserver'
                           'fa-database'
                         when 'mongodb', 'redis', 'elasticsearch'
                           'fa-server'
                         when 'csv', 'excel', 'json', 'xml'
                           'fa-file-alt'
                         when 'api', 'webhook'
                           'fa-plug'
                         when 's3', 'gcs', 'azure_blob'
                           'fa-cloud'
                         when 'quickbooks', 'stripe', 'shopify', 'salesforce'
                           'fa-store'
                         else
                           'fa-database'
                         end %>
                      <i class="fas <%= icon_class %> text-2xl text-neutral-400"></i>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-neutral-900">
                        <%= data_source.name %>
                      </div>
                      <div class="text-sm text-neutral-500">
                        <%= data_source.source_type_text %> • 
                        <%= data_source.sync_frequency_text %>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <div class="mr-4 text-right">
                      <div class="text-sm text-neutral-900">
                        <%= data_source.formatted_row_count %> rows
                      </div>
                      <div class="text-sm text-neutral-500">
                        <% if data_source.last_sync_at %>
                          Last sync <%= time_ago_in_words(data_source.last_sync_at) %> ago
                        <% else %>
                          Never synced
                        <% end %>
                      </div>
                    </div>
                    <div>
                      <% case data_source.status %>
                      <% when 'active' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      <% when 'syncing' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <i class="fas fa-sync-alt animate-spin mr-1"></i>
                          Syncing
                        </span>
                      <% when 'error' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Error
                        </span>
                      <% when 'inactive' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Inactive
                        </span>
                      <% when 'maintenance' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Maintenance
                        </span>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
    <% else %>
      <div class="text-center py-12">
        <i class="fas fa-database text-4xl text-neutral-300 mb-4"></i>
        <h3 class="mt-2 text-sm font-medium text-neutral-900">No data sources</h3>
        <p class="mt-1 text-sm text-neutral-500">Get started by adding a new data source.</p>
        <div class="mt-6">
          <%= link_to new_dashboard_data_source_path, class: "btn btn--primary" do %>
            <i class="fas fa-plus mr-2"></i>
            Add Data Source
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>