<div class="space-y-4" data-controller="csv-form">
  <!-- File Upload or URL -->
  <div>
    <label class="block text-sm font-medium text-neutral-700 mb-3">CSV Source</label>
    
    <!-- Tab selection -->
    <div class="border-b border-neutral-200 mb-4">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button type="button"
                class="csv-source-tab border-b-2 border-primary-500 text-primary-600 py-2 px-1 text-sm font-medium"
                data-tab="upload"
                data-csv-form-target="tabButton"
                data-action="click->csv-form#switchTab">
          <i class="fas fa-upload mr-2"></i>Upload File
        </button>
        <button type="button"
                class="csv-source-tab border-b-2 border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300 py-2 px-1 text-sm font-medium"
                data-tab="url"
                data-csv-form-target="tabButton"
                data-action="click->csv-form#switchTab">
          <i class="fas fa-link mr-2"></i>Remote URL
        </button>
      </nav>
    </div>
    
    <!-- Upload tab content -->
    <div id="csv-upload-tab" class="csv-tab-content" data-csv-form-target="tabContent">
      <div class="max-w-lg">
        <label for="csv_file" class="block">
          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-neutral-300 border-dashed rounded-md hover:border-neutral-400 transition-colors cursor-pointer">
            <div class="space-y-1 text-center">
              <svg class="mx-auto h-12 w-12 text-neutral-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="flex text-sm text-neutral-600">
                <span class="relative font-medium text-primary-600 hover:text-primary-500">
                  Upload a CSV file
                </span>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-neutral-500">CSV files up to 500MB</p>
            </div>
          </div>
          <input id="csv_file" 
                 name="data_source[csv_file]" 
                 type="file" 
                 class="sr-only" 
                 accept=".csv,text/csv"
                 data-csv-form-target="fileInput">
        </label>
        
        <!-- File preview -->
        <div id="csv-file-preview" class="hidden mt-4 p-4 bg-neutral-50 rounded-lg" data-csv-form-target="filePreview">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i class="fas fa-file-csv text-green-600 text-2xl mr-3"></i>
              <div>
                <p class="text-sm font-medium text-neutral-900" id="csv-file-name" data-csv-form-target="fileName"></p>
                <p class="text-xs text-neutral-500" id="csv-file-size" data-csv-form-target="fileSize"></p>
              </div>
            </div>
            <button type="button" data-action="click->csv-form#clearFile" class="text-neutral-400 hover:text-neutral-500">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- URL tab content -->
    <div id="csv-url-tab" class="csv-tab-content hidden" data-csv-form-target="tabContent">
      <input type="text" 
             name="data_source[connection_config][file_url]" 
             value="<%= data_source.connection_config_value('file_url') %>"
             class="form-input w-full" 
             placeholder="https://example.com/data.csv or s3://bucket/file.csv">
      <p class="mt-1 text-sm text-neutral-500">HTTP/HTTPS, FTP, or S3 URL</p>
    </div>
  </div>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
      <label class="block text-sm font-medium text-neutral-700 mb-1">Delimiter</label>
      <select name="data_source[connection_config][delimiter]" class="form-input w-full">
        <option value="">Auto-detect</option>
        <option value="," <%= 'selected' if data_source.connection_config_value('delimiter') == ',' %>>Comma (,)</option>
        <option value=";" <%= 'selected' if data_source.connection_config_value('delimiter') == ';' %>>Semicolon (;)</option>
        <option value="\t" <%= 'selected' if data_source.connection_config_value('delimiter') == "\t" %>>Tab</option>
        <option value="|" <%= 'selected' if data_source.connection_config_value('delimiter') == '|' %>>Pipe (|)</option>
      </select>
    </div>
    
    <div>
      <label class="block text-sm font-medium text-neutral-700 mb-1">Encoding</label>
      <select name="data_source[connection_config][encoding]" class="form-input w-full">
        <option value="">Auto-detect</option>
        <option value="UTF-8" <%= 'selected' if data_source.connection_config_value('encoding') == 'UTF-8' %>>UTF-8</option>
        <option value="ISO-8859-1" <%= 'selected' if data_source.connection_config_value('encoding') == 'ISO-8859-1' %>>ISO-8859-1 (Latin-1)</option>
        <option value="Windows-1252" <%= 'selected' if data_source.connection_config_value('encoding') == 'Windows-1252' %>>Windows-1252</option>
        <option value="ASCII-8BIT" <%= 'selected' if data_source.connection_config_value('encoding') == 'ASCII-8BIT' %>>ASCII-8BIT</option>
      </select>
    </div>
  </div>
  
  <div>
    <label class="flex items-center cursor-pointer">
      <input type="hidden" name="data_source[connection_config][has_headers]" value="false">
      <input type="checkbox" 
             name="data_source[connection_config][has_headers]" 
             value="true"
             <%= 'checked' if data_source.connection_config_value('has_headers') != false %>
             class="form-checkbox mr-2">
      <span class="text-sm text-neutral-700">First row contains headers</span>
    </label>
  </div>
  
  <!-- Authentication for remote files -->
  <div class="border-t pt-4">
    <h4 class="text-sm font-medium text-neutral-700 mb-3">Authentication (for remote files)</h4>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-neutral-700 mb-1">Username</label>
        <input type="text" 
               name="data_source[connection_config][username]" 
               value="<%= data_source.connection_config_value('username') %>"
               class="form-input w-full" 
               placeholder="Optional">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-neutral-700 mb-1">Password</label>
        <input type="password" 
               name="data_source[connection_config][password]" 
               value="<%= data_source.connection_config_value('password') %>"
               class="form-input w-full" 
               placeholder="Optional">
      </div>
    </div>
    
    <div class="mt-4">
      <label class="block text-sm font-medium text-neutral-700 mb-1">S3 Configuration (JSON)</label>
      <textarea name="data_source[connection_config][s3_config]" 
                rows="3" 
                class="form-input w-full font-mono text-sm" 
                placeholder='{"region": "us-east-1", "access_key_id": "...", "secret_access_key": "..."}'><%= data_source.connection_config_value('s3_config') %></textarea>
      <p class="mt-1 text-sm text-neutral-500">For S3 URLs, provide AWS credentials and configuration</p>
    </div>
  </div>
  
  <!-- Transformation Rules -->
  <div class="border-t pt-4">
    <h4 class="text-sm font-medium text-neutral-700 mb-3">Data Transformations (Optional)</h4>
    <textarea name="data_source[connection_config][transformations]" 
              rows="4" 
              class="form-input w-full font-mono text-sm" 
              placeholder='{"field_name": [{"type": "trim"}, {"type": "uppercase"}]}'><%= data_source.connection_config_value('transformations')&.to_json %></textarea>
    <p class="mt-1 text-sm text-neutral-500">JSON configuration for field transformations</p>
  </div>
</div>

