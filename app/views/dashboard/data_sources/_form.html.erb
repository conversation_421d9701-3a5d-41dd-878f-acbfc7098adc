<%= form_with(model: [:dashboard, data_source], local: true, class: "space-y-6", html: { multipart: true }, data: { controller: "data-source-form" }) do |form| %>
  <% if data_source.errors.any? %>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
      <h3 class="font-medium mb-2">Please fix the following errors:</h3>
      <ul class="list-disc list-inside text-sm">
        <% data_source.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <!-- Basic Information -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Basic Information</h2>
    
    <div class="space-y-4">
      <div>
        <%= form.label :name, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.text_field :name, class: "form-input w-full", placeholder: "e.g., Production Database" %>
        <p class="mt-1 text-sm text-neutral-500">A descriptive name for this data source</p>
      </div>

      <div>
        <%= form.label :description, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.text_area :description, rows: 3, class: "form-input w-full", placeholder: "Optional description..." %>
      </div>
    </div>
  </div>

  <!-- Connection Type -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Connection Type</h2>
    
    <!-- Selected type info -->
    <div class="mb-6 p-4 bg-neutral-50 rounded-lg hidden" data-data-source-form-target="typeDescription">
      <!-- Dynamically populated -->
    </div>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
      <% DataSource::SOURCE_TYPES.each do |type| %>
        <label class="relative group">
          <%= form.radio_button :source_type, type, 
              class: "sr-only peer", 
              data: { 
                action: "change->data-source-form#updateConnectionFields",
                "data-source-form-target": "sourceTypeRadio"
              } %>
          <div class="flex flex-col p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 
                      border-neutral-200 hover:border-neutral-300 hover:shadow-sm
                      peer-checked:border-primary-600 peer-checked:bg-primary-50">
            <div class="flex items-center mb-2">
              <i class="<%= data_source_icon(type) %> text-xl mr-3 text-neutral-400 peer-checked:text-primary-600 group-hover:text-neutral-600"></i>
              <span class="text-sm font-medium text-neutral-900"><%= type.humanize %></span>
            </div>
            <% case type %>
            <% when 'postgresql', 'mysql' %>
              <span class="text-xs text-neutral-500">Database connector</span>
            <% when 'csv', 'excel', 'json', 'xml' %>
              <span class="text-xs text-neutral-500">File import</span>
            <% when 'api', 'webhook' %>
              <span class="text-xs text-neutral-500">API integration</span>
            <% when 's3', 'gcs', 'azure_blob' %>
              <span class="text-xs text-neutral-500">Cloud storage</span>
            <% when 'salesforce', 'stripe', 'shopify', 'quickbooks' %>
              <span class="text-xs text-neutral-500">SaaS connector</span>
            <% else %>
              <span class="text-xs text-neutral-500">Data source</span>
            <% end %>
          </div>
          <div class="absolute top-2 right-2 opacity-0 peer-checked:opacity-100 transition-opacity">
            <i class="fas fa-check-circle text-primary-600"></i>
          </div>
        </label>
      <% end %>
    </div>
    
    <!-- Features for selected type -->
    <div class="hidden border-t pt-4" data-data-source-form-target="typeFeatures">
      <!-- Dynamically populated -->
    </div>
  </div>

  <!-- Connection Settings -->
  <div class="bg-white shadow rounded-lg p-6" data-data-source-form-target="connectionFields">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Connection Settings</h2>
    
    <div id="connection-settings" class="space-y-4">
      <!-- Dynamic fields will be inserted here based on source type -->
      <% if data_source.persisted? && data_source.source_type.present? %>
        <%= render partial: "dashboard/data_sources/connection_fields/#{data_source.source_type}", locals: { form: form, data_source: data_source } %>
      <% else %>
        <p class="text-neutral-500 text-sm">Select a connection type above to configure settings.</p>
      <% end %>
    </div>
  </div>

  <!-- Sync Settings -->
  <div class="bg-white shadow rounded-lg p-6" data-data-source-form-target="syncSettings">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Sync Settings</h2>
    
    <div class="space-y-4">
      <div>
        <%= form.label :sync_frequency, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.select :sync_frequency, 
            options_for_select([
              ['Manual Only', 'manual'],
              ['Real-time', 'realtime'],
              ['Every 5 minutes', 'every_5_minutes'],
              ['Every 15 minutes', 'every_15_minutes'],
              ['Every 30 minutes', 'every_30_minutes'],
              ['Hourly', 'hourly'],
              ['Every 6 hours', 'every_6_hours'],
              ['Daily', 'daily'],
              ['Weekly', 'weekly'],
              ['Monthly', 'monthly']
            ], data_source.sync_frequency),
            { prompt: 'Select frequency...' },
            class: "form-input w-full" %>
        <div class="sync-recommendation"></div>
      </div>

      <div>
        <label class="flex items-center cursor-pointer">
          <%= form.check_box :active, class: "form-checkbox mr-2" %>
          <span class="text-sm text-neutral-700">Enable automatic syncing</span>
        </label>
        <p class="mt-1 text-sm text-neutral-500 ml-6">When enabled, data will sync automatically based on the frequency above</p>
      </div>
      
      <!-- Advanced sync options (hidden by default, shown for certain types) -->
      <div class="border-t pt-4 mt-4 hidden" data-form-section="advanced-sync">
        <h3 class="text-sm font-medium text-neutral-700 mb-3">Advanced Options</h3>
        
        <div class="space-y-3">
          <label class="flex items-center cursor-pointer">
            <%= form.check_box :incremental_sync, class: "form-checkbox mr-2" %>
            <span class="text-sm text-neutral-700">Enable incremental sync</span>
          </label>
          
          <label class="flex items-center cursor-pointer">
            <%= form.check_box :deduplicate, class: "form-checkbox mr-2" %>
            <span class="text-sm text-neutral-700">Remove duplicate records</span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex justify-end space-x-3">
    <%= link_to "Cancel", dashboard_data_sources_path, class: "btn btn--secondary" %>
    <%= form.submit data_source.persisted? ? "Update Data Source" : "Create Data Source", class: "btn btn--primary" %>
  </div>
<% end %>