# frozen_string_literal: true

module ApplicationHelper
  # Display flash messages with appropriate styling
  def flash_class(level)
    case level.to_sym
    when :notice, :success
      "bg-green-50 border-green-200 text-green-800"
    when :alert, :error
      "bg-red-50 border-red-200 text-red-700"
    when :warning
      "bg-yellow-50 border-yellow-200 text-yellow-800"
    else
      "bg-blue-50 border-blue-200 text-blue-800"
    end
  end
  
  # Generate page title
  def page_title(title = nil)
    base_title = "Data Reflow"
    title.present? ? "#{title} | #{base_title}" : base_title
  end
  
  # Check if current page
  def current_page_class(path)
    current_page?(path) ? "text-teal-600 font-semibold" : "text-gray-700"
  end
  
  # Format currency
  def format_currency(amount, currency = "$")
    "#{currency}#{number_with_delimiter(amount)}"
  end
  
  # Format percentage
  def format_percentage(value, decimals = 0)
    "#{number_with_precision(value, precision: decimals)}%"
  end
  
  # Render SVG icon
  def svg_icon(name, css_class = "w-6 h-6")
    file_path = Rails.root.join('app', 'assets', 'images', 'icons', "#{name}.svg")
    return "" unless File.exist?(file_path)
    
    svg_content = File.read(file_path)
    svg_content.gsub!('<svg', "<svg class=\"#{css_class}\"")
    svg_content.html_safe
  end
  
  # Render feature icon based on name
  def feature_icon(icon_name)
    icons = {
      "pipeline" => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />',
      "ai-brain" => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />',
      "templates" => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />',
      "collaboration" => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
      "security" => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />',
      "api" => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />'
    }
    
    icons[icon_name] || icons["pipeline"]
  end
  
  # Generate meta tags for SEO
  def meta_tags(options = {})
    defaults = {
      site: "Data Reflow",
      title: @page_title || "Enterprise-Grade Data Analytics for SMEs",
      description: @meta_description || "Transform raw data into actionable insights. Reduce costs by 30%, increase efficiency by 40%.",
      image: image_url("og-image.png"),
      twitter: {
        card: "summary_large_image",
        site: "@datareflow",
      }
    }
    
    options.reverse_merge!(defaults)
    
    tags = []
    tags << content_tag(:title, options[:title])
    tags << tag(:meta, name: "description", content: options[:description])
    
    # Open Graph tags
    tags << tag(:meta, property: "og:site_name", content: options[:site])
    tags << tag(:meta, property: "og:title", content: options[:title])
    tags << tag(:meta, property: "og:description", content: options[:description])
    tags << tag(:meta, property: "og:image", content: options[:image])
    tags << tag(:meta, property: "og:type", content: "website")
    
    # Twitter Card tags
    tags << tag(:meta, name: "twitter:card", content: options[:twitter][:card])
    tags << tag(:meta, name: "twitter:site", content: options[:twitter][:site])
    tags << tag(:meta, name: "twitter:title", content: options[:title])
    tags << tag(:meta, name: "twitter:description", content: options[:description])
    tags << tag(:meta, name: "twitter:image", content: options[:image])
    
    safe_join(tags, "\n")
  end
  
  # Format date with relative time
  def format_date_with_relative(date)
    return "" unless date
    
    time_ago = time_ago_in_words(date)
    formatted_date = date.strftime("%B %d, %Y")
    
    content_tag(:time, datetime: date.iso8601, title: formatted_date) do
      "#{time_ago} ago"
    end
  end
  
  # Check if user has access to feature
  def feature_available?(feature_name)
    return false unless current_user&.active_subscription
    current_user.active_subscription.plan.features.include?(feature_name.to_s)
  end
  
  # Render plan badge
  def plan_badge(plan_name, size = "normal")
    css_classes = case size
                  when "small"
                    "px-2 py-1 text-xs"
                  when "large"
                    "px-4 py-2 text-base"
                  else
                    "px-3 py-1 text-sm"
                  end
    
    badge_color = case plan_name.downcase
                  when "starter"
                    "bg-gray-100 text-gray-800"
                  when "growth"
                    "bg-teal-100 text-teal-800"
                  when "enterprise"
                    "bg-purple-100 text-purple-800"
                  else
                    "bg-blue-100 text-blue-800"
                  end
    
    content_tag(:span, plan_name, class: "#{css_classes} #{badge_color} rounded-full font-medium")
  end
  
  # Get icon for data source type
  def data_source_icon(type)
    icons = {
      "postgresql" => "fas fa-database",
      "mysql" => "fas fa-database",
      "sqlite" => "fas fa-database",
      "sqlserver" => "fab fa-microsoft",
      "mongodb" => "fas fa-leaf",
      "redis" => "fas fa-bolt",
      "elasticsearch" => "fas fa-search",
      "csv" => "fas fa-file-csv",
      "excel" => "fas fa-file-excel",
      "json" => "fas fa-code",
      "xml" => "fas fa-file-code",
      "api" => "fas fa-plug",
      "webhook" => "fas fa-satellite-dish",
      "stream" => "fas fa-stream",
      "s3" => "fab fa-aws",
      "gcs" => "fab fa-google",
      "azure_blob" => "fab fa-microsoft",
      "sftp" => "fas fa-server",
      "quickbooks" => "fas fa-calculator",
      "stripe" => "fab fa-stripe",
      "shopify" => "fas fa-shopping-cart",
      "salesforce" => "fab fa-salesforce"
    }
    
    icons[type] || "fas fa-question-circle"
  end
end
