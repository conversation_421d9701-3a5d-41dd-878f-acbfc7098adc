class DataSource < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant :organization
  
  # Associations
  belongs_to :organization
  has_many :integrations, dependent: :destroy
  has_many :pipelines, dependent: :destroy
  
  # Encryption for sensitive connection config
  encrypts :connection_config
  
  # Constants
  SOURCE_TYPES = %w[
    postgresql mysql sqlite sqlserver
    mongodb redis elasticsearch
    csv excel json xml
    api webhook stream
    s3 gcs azure_blob sftp
    quickbooks stripe shopify salesforce
  ].freeze
  
  STATUSES = %w[inactive active syncing error maintenance].freeze
  
  SYNC_FREQUENCIES = %w[
    manual
    realtime
    every_5_minutes
    every_15_minutes
    every_30_minutes
    hourly
    every_6_hours
    daily
    weekly
    monthly
  ].freeze
  
  # Validations
  validates :name, presence: true, 
            uniqueness: { scope: :organization_id, case_sensitive: false }
  validates :source_type, presence: true, inclusion: { in: SOURCE_TYPES }
  validates :status, presence: true, inclusion: { in: STATUSES }
  validates :sync_frequency, inclusion: { in: SYNC_FREQUENCIES }, allow_nil: true
  
  # Scopes
  scope :active, -> { where(status: 'active') }
  scope :inactive, -> { where(status: 'inactive') }
  scope :with_errors, -> { where(status: 'error') }
  scope :by_type, ->(type) { where(source_type: type) }
  scope :needs_sync, -> { 
    active.where(
      "last_sync_at IS NULL OR last_sync_at < ?", 
      1.hour.ago
    )
  }
  
  # Callbacks
  before_validation :normalize_name
  after_update :clear_error_if_fixed
  
  # Instance methods
  def active?
    status == 'active'
  end
  
  def inactive?
    status == 'inactive'
  end
  
  def has_error?
    status == 'error'
  end
  
  def syncing?
    status == 'syncing'
  end
  
  def can_sync?
    active? && !syncing?
  end
  
  def database_source?
    %w[postgresql mysql sqlite sqlserver mongodb redis elasticsearch].include?(source_type)
  end
  
  def file_source?
    %w[csv excel json xml].include?(source_type)
  end
  
  def api_source?
    %w[api webhook stream].include?(source_type)
  end
  
  def cloud_storage_source?
    %w[s3 gcs azure_blob sftp].include?(source_type)
  end
  
  def saas_source?
    %w[quickbooks stripe shopify salesforce].include?(source_type)
  end
  
  # Connection configuration helpers
  def connection_settings
    return {} if connection_config.blank?
    
    begin
      JSON.parse(connection_config).with_indifferent_access
    rescue JSON::ParserError
      {}
    end
  end
  
  def update_connection_settings(settings)
    self.connection_config = settings.to_json
    save
  end
  
  # Test connection
  def test_connection
    connector = connector_class.new(self)
    result = connector.test_connection
    
    if result[:success]
      update!(status: 'active', error_message: nil)
    else
      update!(status: 'error', error_message: result[:error])
    end
    
    result
  rescue => e
    update!(status: 'error', error_message: e.message)
    { success: false, error: e.message }
  end
  
  # Sync methods
  def sync!
    return { success: false, error: 'Cannot sync inactive source' } unless can_sync?
    
    update!(status: 'syncing', error_message: nil)
    
    # Queue the sync job
    DataSourceSyncJob.perform_later(self)
    
    { success: true, message: 'Sync started' }
  rescue => e
    update!(status: 'error', error_message: e.message)
    { success: false, error: e.message }
  end
  
  def sync_now!
    return { success: false, error: 'Cannot sync inactive source' } unless can_sync?
    
    update!(status: 'syncing', error_message: nil)
    
    connector = connector_class.new(self)
    result = connector.sync
    
    if result[:success]
      update!(
        status: 'active',
        last_sync_at: Time.current,
        row_count: result[:row_count] || 0,
        error_message: nil,
        metadata: metadata.merge(last_sync_result: result[:metadata] || {})
      )
    else
      update!(
        status: 'error',
        error_message: result[:error],
        metadata: metadata.merge(last_sync_error: result[:error_details] || {})
      )
    end
    
    result
  rescue => e
    update!(status: 'error', error_message: e.message)
    { success: false, error: e.message }
  end
  
  # Get schema/structure
  def fetch_schema
    connector = connector_class.new(self)
    connector.fetch_schema
  rescue => e
    { success: false, error: e.message }
  end
  
  # Get sample data
  def fetch_sample_data(limit: 10)
    connector = connector_class.new(self)
    connector.fetch_sample_data(limit: limit)
  rescue => e
    { success: false, error: e.message }
  end
  
  # Scheduling
  def schedule_next_sync
    return unless active? && sync_frequency != 'manual'
    
    next_sync_time = calculate_next_sync_time
    DataSourceSyncJob.set(wait_until: next_sync_time).perform_later(self)
  end
  
  def calculate_next_sync_time
    case sync_frequency
    when 'realtime'
      1.minute.from_now
    when 'every_5_minutes'
      5.minutes.from_now
    when 'every_15_minutes'
      15.minutes.from_now
    when 'every_30_minutes'
      30.minutes.from_now
    when 'hourly'
      1.hour.from_now
    when 'every_6_hours'
      6.hours.from_now
    when 'daily'
      1.day.from_now
    when 'weekly'
      1.week.from_now
    when 'monthly'
      1.month.from_now
    else
      nil
    end
  end
  
  def connection_config_value(key)
    return nil if connection_config.blank?
    
    config = connection_config.is_a?(String) ? JSON.parse(connection_config) : connection_config
    config ||= {}
    config[key.to_s]
  rescue JSON::ParserError
    nil
  end
  
  # Formatted helpers
  def formatted_row_count
    return 'No data' if row_count.nil? || row_count.zero?
    
    number_with_delimiter(row_count)
  end
  
  def sync_frequency_text
    sync_frequency.humanize.gsub('_', ' ').capitalize
  end
  
  def source_type_text
    case source_type
    when 'postgresql' then 'PostgreSQL'
    when 'mysql' then 'MySQL'
    when 'sqlite' then 'SQLite'
    when 'sqlserver' then 'SQL Server'
    when 'mongodb' then 'MongoDB'
    when 'redis' then 'Redis'
    when 'elasticsearch' then 'Elasticsearch'
    when 'csv' then 'CSV File'
    when 'excel' then 'Excel File'
    when 'json' then 'JSON File'
    when 'xml' then 'XML File'
    when 'api' then 'REST API'
    when 'webhook' then 'Webhook'
    when 'stream' then 'Data Stream'
    when 's3' then 'Amazon S3'
    when 'gcs' then 'Google Cloud Storage'
    when 'azure_blob' then 'Azure Blob Storage'
    when 'sftp' then 'SFTP'
    when 'quickbooks' then 'QuickBooks'
    when 'stripe' then 'Stripe'
    when 'shopify' then 'Shopify'
    when 'salesforce' then 'Salesforce'
    else
      source_type.humanize
    end
  end
  
  private
  
  def normalize_name
    self.name = name&.strip
  end
  
  def clear_error_if_fixed
    if saved_change_to_status? && status != 'error' && error_message.present?
      self.error_message = nil
      save
    end
  end
  
  def connector_class
    "Connectors::#{source_type.camelize}Connector".constantize
  rescue NameError
    raise NotImplementedError, "Connector not implemented for #{source_type}"
  end
  
  def number_with_delimiter(number)
    number.to_s.gsub(/(\d)(?=(\d{3})+(?!\d))/, '\1,')
  end
end