class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :trackable
         
  acts_as_tenant :organization
  
  # Custom validations (replacing <PERSON><PERSON>'s validatable module)
  validates :email, presence: true, 
            uniqueness: { scope: :organization_id, case_sensitive: false },
            format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, presence: true, length: { minimum: 6 }, if: :password_required?
  validates :password, confirmation: true, if: :password_required?
  validates :role, inclusion: { in: %w[admin member viewer] }
  
  before_save { self.email = email.downcase }
  
  scope :active, -> { where(active: true) }
  scope :admins, -> { where(role: 'admin') }
  
  def full_name
    "#{first_name} #{last_name}".strip.presence || email
  end

  def initials
    if first_name.present? && last_name.present?
      "#{first_name.first}#{last_name.first}".upcase
    elsif first_name.present?
      first_name.first(2).upcase
    elsif email.present?
      email.first(2).upcase
    else
      "NA"
    end
  end
  
  def admin?
    role == 'admin'
  end
  
  def member?
    role == 'member'
  end
  
  def viewer?
    role == 'viewer'
  end
  
  def can_create_connectors?
    %w[admin member].include?(role)
  end
  
  def can_edit_pipelines?
    %w[admin member].include?(role)
  end
  
  def can_manage_organization?
    admin?
  end
  
  def record_login!
    update_column(:last_login_at, Time.current)
  end
  
  private
  
  def password_required?
    new_record? || password.present? || password_confirmation.present?
  end
end