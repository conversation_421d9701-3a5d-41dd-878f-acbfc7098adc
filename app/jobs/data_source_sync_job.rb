class DataSourceSyncJob < ApplicationJob
  queue_as :default
  
  # Retry configuration
  retry_on StandardError, wait: :polynomially_longer, attempts: 3
  
  # Ensure we don't have duplicate sync jobs
  discard_on ActiveJob::DeserializationError
  
  def perform(data_source)
    # Set tenant context
    ActsAsTenant.with_tenant(data_source.organization) do
      Rails.logger.info "Starting sync for DataSource #{data_source.id} - #{data_source.name}"
      
      # Perform the sync
      result = data_source.sync_now!
      
      if result[:success]
        Rails.logger.info "Sync completed for DataSource #{data_source.id}"
        
        # Schedule next sync if needed
        data_source.schedule_next_sync
        
        # Notify user of successful sync (if implemented)
        # DataSourceMailer.sync_completed(data_source).deliver_later
      else
        Rails.logger.error "Sync failed for DataSource #{data_source.id}: #{result[:error]}"
        
        # Notify user of failed sync (if implemented)
        # DataSourceMailer.sync_failed(data_source, result[:error]).deliver_later
      end
      
      result
    end
  rescue => e
    Rails.logger.error "Unexpected error in DataSourceSyncJob for #{data_source.id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    
    # Update data source status
    data_source.update!(
      status: 'error',
      error_message: "Sync job error: #{e.message}"
    )
    
    raise # Re-raise to trigger retry
  end
end