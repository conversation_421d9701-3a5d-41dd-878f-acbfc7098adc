require 'pg'

module Connectors
  class PostgresqlConnector < BaseConnector
    def test_connection
      validate_required_settings(:host, :port, :database, :username)
      
      connection = establish_connection
      
      # Test with a simple query
      result = connection.exec("SELECT version()")
      version = result.first['version']
      
      connection.close
      
      {
        success: true,
        message: "Successfully connected to PostgreSQL",
        metadata: {
          version: version,
          database: connection_settings[:database],
          host: connection_settings[:host]
        }
      }
    rescue PG::Error => e
      handle_connection_error(e)
    rescue => e
      handle_connection_error(e)
    end
    
    def sync
      validate_required_settings(:host, :port, :database, :username)
      
      log_sync_activity('sync_started', sanitized_settings)
      
      connection = establish_connection
      total_rows = 0
      
      # Get all tables in the specified schema (default: public)
      schema = connection_settings[:schema] || 'public'
      tables_query = <<-SQL
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = $1 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
      SQL
      
      tables_result = connection.exec_params(tables_query, [schema])
      tables = tables_result.map { |row| row['table_name'] }
      
      # Count rows in each table
      tables.each do |table|
        count_result = connection.exec("SELECT COUNT(*) FROM #{connection.quote_ident(schema)}.#{connection.quote_ident(table)}")
        count = count_result.first['count'].to_i
        total_rows += count
      end
      
      connection.close
      
      log_sync_activity('sync_completed', { 
        total_rows: total_rows, 
        table_count: tables.length 
      })
      
      {
        success: true,
        row_count: total_rows,
        metadata: {
          tables: tables,
          table_count: tables.length,
          schema: schema,
          synced_at: Time.current
        }
      }
    rescue PG::Error => e
      handle_connection_error(e)
    rescue => e
      handle_connection_error(e)
    end
    
    def fetch_schema
      validate_required_settings(:host, :port, :database, :username)
      
      connection = establish_connection
      schema = connection_settings[:schema] || 'public'
      
      # Get detailed table and column information
      schema_query = <<-SQL
        SELECT 
          t.table_name,
          c.column_name,
          c.data_type,
          c.character_maximum_length,
          c.numeric_precision,
          c.numeric_scale,
          c.is_nullable,
          c.column_default,
          tc.constraint_type
        FROM information_schema.tables t
        JOIN information_schema.columns c 
          ON t.table_name = c.table_name 
          AND t.table_schema = c.table_schema
        LEFT JOIN information_schema.key_column_usage kcu
          ON c.table_name = kcu.table_name
          AND c.column_name = kcu.column_name
          AND c.table_schema = kcu.table_schema
        LEFT JOIN information_schema.table_constraints tc
          ON kcu.constraint_name = tc.constraint_name
          AND kcu.table_schema = tc.table_schema
        WHERE t.table_schema = $1
          AND t.table_type = 'BASE TABLE'
        ORDER BY t.table_name, c.ordinal_position
      SQL
      
      result = connection.exec_params(schema_query, [schema])
      
      # Organize by table
      schema_info = {}
      result.each do |row|
        table_name = row['table_name']
        schema_info[table_name] ||= { columns: [], primary_keys: [] }
        
        column_info = {
          name: row['column_name'],
          type: row['data_type'],
          nullable: row['is_nullable'] == 'YES',
          default: row['column_default'],
          max_length: row['character_maximum_length'],
          precision: row['numeric_precision'],
          scale: row['numeric_scale']
        }
        
        schema_info[table_name][:columns] << column_info
        
        if row['constraint_type'] == 'PRIMARY KEY'
          schema_info[table_name][:primary_keys] << row['column_name']
        end
      end
      
      connection.close
      
      {
        success: true,
        schema: schema_info,
        metadata: {
          database: connection_settings[:database],
          schema: schema,
          table_count: schema_info.keys.length,
          fetched_at: Time.current
        }
      }
    rescue PG::Error => e
      handle_connection_error(e)
    rescue => e
      handle_connection_error(e)
    end
    
    def fetch_sample_data(limit: 10)
      validate_required_settings(:host, :port, :database, :username)
      
      connection = establish_connection
      schema = connection_settings[:schema] || 'public'
      table = connection_settings[:table] || fetch_first_table(connection, schema)
      
      return { success: false, error: 'No table specified or found' } if table.nil?
      
      # Fetch sample data
      query = "SELECT * FROM #{connection.quote_ident(schema)}.#{connection.quote_ident(table)} LIMIT $1"
      result = connection.exec_params(query, [limit])
      
      # Convert to array of hashes
      data = result.map { |row| row.to_h }
      
      connection.close
      
      {
        success: true,
        data: data,
        metadata: {
          table: table,
          schema: schema,
          row_count: data.length,
          columns: data.first&.keys || []
        }
      }
    rescue PG::Error => e
      handle_connection_error(e)
    rescue => e
      handle_connection_error(e)
    end
    
    def execute_query(query)
      validate_required_settings(:host, :port, :database, :username)
      
      connection = establish_connection
      
      # Execute the query
      result = connection.exec(query)
      
      # Convert to array of hashes
      data = result.map { |row| row.to_h }
      
      connection.close
      
      {
        success: true,
        data: data,
        metadata: {
          row_count: data.length,
          columns: data.first&.keys || [],
          executed_at: Time.current
        }
      }
    rescue PG::Error => e
      handle_connection_error(e)
    rescue => e
      handle_connection_error(e)
    end
    
    private
    
    def establish_connection
      connection_params = {
        host: connection_settings[:host],
        port: connection_settings[:port] || 5432,
        dbname: connection_settings[:database],
        user: connection_settings[:username],
        password: connection_settings[:password],
        connect_timeout: connection_settings[:timeout] || 10
      }
      
      # Add SSL settings if provided
      if connection_settings[:ssl_mode].present?
        connection_params[:sslmode] = connection_settings[:ssl_mode]
      end
      
      PG.connect(connection_params)
    end
    
    def fetch_first_table(connection, schema)
      query = <<-SQL
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = $1 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
        LIMIT 1
      SQL
      
      result = connection.exec_params(query, [schema])
      result.first&.fetch('table_name')
    end
  end
end