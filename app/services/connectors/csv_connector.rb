module Connectors
  class CsvConnector < BaseConnector
    SUPPORTED_ENCODINGS = %w[UTF-8 ISO-8859-1 Windows-1252 ASCII-8BIT].freeze
    MAX_FILE_SIZE = 500.megabytes
    CHUNK_SIZE = 10_000
    
    def test_connection
      validate_required_settings(:file_path)
      
      file_info = analyze_file
      
      {
        success: true,
        message: "Successfully connected to CSV file",
        details: {
          file_size: file_info[:size],
          encoding: file_info[:encoding],
          delimiter: file_info[:delimiter],
          headers: file_info[:headers],
          row_count_estimate: file_info[:row_count_estimate]
        }
      }
    rescue => e
      handle_error(e)
    end
    
    def fetch_schema
      validate_required_settings(:file_path)
      
      headers, sample_data = read_file_headers_and_sample
      
      schema = headers.map do |header|
        column_data = sample_data.map { |row| row[header] }.compact
        
        {
          name: header,
          type: infer_column_type(column_data),
          nullable: column_data.size < sample_data.size,
          sample_values: column_data.first(5)
        }
      end
      
      {
        success: true,
        schema: {
          columns: schema,
          row_count_estimate: estimate_row_count,
          file_info: analyze_file
        }
      }
    rescue => e
      handle_error(e)
    end
    
    def fetch_sample_data(limit: 10)
      validate_required_settings(:file_path)
      
      rows = []
      headers = nil
      
      read_csv do |csv|
        headers = csv.headers
        csv.each_with_index do |row, index|
          break if index >= limit
          rows << row.to_h
        end
      end
      
      {
        success: true,
        data: rows,
        headers: headers,
        total_rows: rows.size
      }
    rescue => e
      handle_error(e)
    end
    
    def sync
      validate_required_settings(:file_path)
      
      start_time = Time.current
      total_rows = 0
      errors = []
      
      # Create temporary table for staging
      staging_table = create_staging_table
      
      begin
        # Process file in chunks
        read_csv do |csv|
          batch = []
          
          csv.each do |row|
            batch << process_row(row)
            
            if batch.size >= CHUNK_SIZE
              result = insert_batch(staging_table, batch)
              total_rows += result[:inserted]
              errors.concat(result[:errors]) if result[:errors].any?
              batch.clear
            end
          end
          
          # Insert remaining rows
          if batch.any?
            result = insert_batch(staging_table, batch)
            total_rows += result[:inserted]
            errors.concat(result[:errors]) if result[:errors].any?
          end
        end
        
        # Move data from staging to final table
        finalize_sync(staging_table)
        
        {
          success: true,
          row_count: total_rows,
          duration: Time.current - start_time,
          errors: errors.first(100), # Limit error reporting
          metadata: {
            file_path: connection_settings['file_path'],
            sync_completed_at: Time.current,
            warnings: generate_warnings(errors)
          }
        }
      rescue => e
        cleanup_staging_table(staging_table)
        handle_error(e)
      end
    end
    
    private
    
    def analyze_file
      file_path = get_file_path
      
      if remote_file?
        analyze_remote_file(file_path)
      else
        analyze_local_file(file_path)
      end
    end
    
    def get_file_path
      connection_settings['file_url'] || connection_settings['file_path']
    end
    
    def analyze_remote_file(url)
      response = URI.open(url, read_timeout: 30)
      
      {
        size: response.size || 0,
        encoding: detect_encoding(response),
        delimiter: detect_delimiter(response),
        headers: detect_headers(response),
        row_count_estimate: estimate_row_count_from_sample(response)
      }
    end
    
    def analyze_local_file(path)
      raise "File not found: #{path}" unless File.exist?(path)
      raise "File too large: #{path}" if File.size(path) > MAX_FILE_SIZE
      
      sample = File.read(path, 10_000)
      
      {
        size: File.size(path),
        encoding: detect_encoding(sample),
        delimiter: detect_delimiter(sample),
        headers: detect_headers(sample),
        row_count_estimate: estimate_row_count
      }
    end
    
    def detect_encoding(sample)
      # Try to detect encoding from BOM or content
      return 'UTF-8' if sample.start_with?("\xEF\xBB\xBF")
      return 'UTF-16LE' if sample.start_with?("\xFF\xFE")
      return 'UTF-16BE' if sample.start_with?("\xFE\xFF")
      
      # Try different encodings
      SUPPORTED_ENCODINGS.each do |encoding|
        begin
          sample.force_encoding(encoding).valid_encoding?
          return encoding
        rescue
          next
        end
      end
      
      'UTF-8' # Default fallback
    end
    
    def detect_delimiter(sample)
      lines = sample.lines.first(10)
      
      delimiters = {
        ',' => 0,
        ';' => 0,
        "\t" => 0,
        '|' => 0
      }
      
      lines.each do |line|
        delimiters.each_key do |delimiter|
          delimiters[delimiter] += line.count(delimiter)
        end
      end
      
      delimiters.max_by { |_, count| count }.first
    end
    
    def detect_headers(sample)
      delimiter = connection_settings['delimiter'] || detect_delimiter(sample)
      first_line = sample.lines.first
      
      CSV.parse_line(first_line, col_sep: delimiter)
    end
    
    def read_file_headers_and_sample
      headers = []
      sample_data = []
      
      read_csv do |csv|
        headers = csv.headers
        csv.each_with_index do |row, index|
          break if index >= 1000 # Sample size for type inference
          sample_data << row.to_h
        end
      end
      
      [headers, sample_data]
    end
    
    def read_csv(&block)
      options = csv_options
      
      if remote_file?
        read_remote_csv(options, &block)
      else
        read_local_csv(options, &block)
      end
    end
    
    def read_remote_csv(options)
      file_path = get_file_path
      
      URI.open(file_path, read_timeout: 300) do |file|
        CSV.parse(file, **options) do |row|
          yield CSV::Row.new(options[:headers], row)
        end
      end
    end
    
    def read_local_csv(options)
      file_path = get_file_path
      
      CSV.foreach(file_path, **options) do |row|
        yield row
      end
    end
    
    def csv_options
      {
        headers: connection_settings['has_headers'] != false,
        col_sep: connection_settings['delimiter'] || detect_delimiter_from_file,
        encoding: connection_settings['encoding'] || detect_encoding_from_file,
        skip_blanks: true,
        liberal_parsing: true
      }
    end
    
    def detect_delimiter_from_file
      file_path = get_file_path
      sample = remote_file? ? 
        URI.open(file_path).read(10_000) : 
        File.read(file_path, 10_000)
      
      detect_delimiter(sample)
    end
    
    def detect_encoding_from_file
      file_path = get_file_path
      sample = remote_file? ? 
        URI.open(file_path).read(10_000) : 
        File.read(file_path, 10_000)
      
      detect_encoding(sample)
    end
    
    def remote_file?
      file_path_or_url = connection_settings['file_url'] || connection_settings['file_path']
      file_path_or_url&.start_with?('http://', 'https://', 'ftp://', 's3://')
    end
    
    def estimate_row_count
      if remote_file?
        estimate_row_count_from_remote
      else
        estimate_row_count_from_local
      end
    end
    
    def estimate_row_count_from_local
      file_path = get_file_path
      file_size = File.size(file_path)
      
      # Read first 1000 lines to get average row size
      sample_size = 0
      line_count = 0
      
      File.foreach(file_path).with_index do |line, index|
        break if index >= 1000
        sample_size += line.bytesize
        line_count += 1
      end
      
      avg_row_size = sample_size.to_f / line_count
      (file_size / avg_row_size).to_i
    end
    
    def estimate_row_count_from_remote
      # For remote files, we can't easily estimate without downloading
      # Return nil to indicate unknown
      nil
    end
    
    def estimate_row_count_from_sample(sample)
      lines = sample.lines
      return 0 if lines.empty?
      
      # Rough estimate based on sample
      avg_line_size = sample.bytesize.to_f / lines.size
      total_size = sample.respond_to?(:size) ? sample.size : sample.bytesize
      
      (total_size / avg_line_size).to_i
    end
    
    def infer_column_type(values)
      return 'string' if values.empty?
      
      # Check if all values match a specific type
      if values.all? { |v| v.nil? || v.to_s.match?(/^\d+$/) }
        'integer'
      elsif values.all? { |v| v.nil? || v.to_s.match?(/^\d*\.?\d+$/) }
        'decimal'
      elsif values.all? { |v| v.nil? || v.to_s.match?(/^(true|false|1|0|yes|no)$/i) }
        'boolean'
      elsif values.all? { |v| v.nil? || valid_date?(v) }
        'date'
      elsif values.all? { |v| v.nil? || valid_datetime?(v) }
        'datetime'
      else
        'string'
      end
    end
    
    def valid_date?(value)
      Date.parse(value.to_s)
      true
    rescue
      false
    end
    
    def valid_datetime?(value)
      DateTime.parse(value.to_s)
      true
    rescue
      false
    end
    
    def process_row(row)
      # Apply transformations if configured
      transformed = row.to_h
      
      if connection_settings['transformations'].present?
        apply_transformations(transformed)
      end
      
      # Add metadata
      transformed['_imported_at'] = Time.current
      transformed['_source_file'] = get_file_path
      transformed['_row_number'] = row.lineno if row.respond_to?(:lineno)
      
      transformed
    end
    
    def apply_transformations(row)
      transformations = connection_settings['transformations']
      
      transformations.each do |field, rules|
        next unless row.key?(field)
        
        value = row[field]
        
        rules.each do |rule|
          case rule['type']
          when 'trim'
            value = value&.strip
          when 'lowercase'
            value = value&.downcase
          when 'uppercase'
            value = value&.upcase
          when 'replace'
            value = value&.gsub(rule['from'], rule['to'])
          when 'date_format'
            value = format_date(value, rule['format'])
          when 'number_format'
            value = format_number(value, rule['decimals'])
          end
        end
        
        row[field] = value
      end
    end
    
    def format_date(value, format)
      return nil if value.nil? || value.to_s.empty?
      
      date = Date.parse(value.to_s)
      date.strftime(format)
    rescue
      value
    end
    
    def format_number(value, decimals)
      return nil if value.nil? || value.to_s.empty?
      
      number = value.to_f
      decimals ? number.round(decimals) : number.to_i
    rescue
      value
    end
    
    def create_staging_table
      # Implementation depends on target database
      # This is a placeholder - actual implementation would create
      # a temporary table in the target database
      "staging_#{data_source.id}_#{Time.current.to_i}"
    end
    
    def insert_batch(table, batch)
      # Implementation depends on target database
      # This would bulk insert the batch into the staging table
      {
        inserted: batch.size,
        errors: []
      }
    end
    
    def finalize_sync(staging_table)
      # Move data from staging to final table
      # Implementation depends on target database
    end
    
    def cleanup_staging_table(staging_table)
      # Drop the staging table
      # Implementation depends on target database
    end
    
    def generate_warnings(errors)
      warnings = []
      
      if errors.any? { |e| e.include?('encoding') }
        warnings << "Some rows had encoding issues. Consider specifying the correct encoding."
      end
      
      if errors.any? { |e| e.include?('parse') }
        warnings << "Some rows could not be parsed. Check delimiter and format settings."
      end
      
      if errors.size >= 100
        warnings << "Error limit reached. There may be more errors not shown."
      end
      
      warnings
    end
    
    def handle_error(error)
      case error
      when Errno::ENOENT
        { success: false, error: "File not found: #{connection_settings['file_path']}" }
      when OpenURI::HTTPError
        { success: false, error: "Failed to download file: #{error.message}" }
      when CSV::MalformedCSVError
        { success: false, error: "Invalid CSV format: #{error.message}" }
      when Encoding::UndefinedConversionError, Encoding::InvalidByteSequenceError
        { success: false, error: "Encoding error: #{error.message}. Try specifying a different encoding." }
      else
        { success: false, error: error.message }
      end
    end
  end
end